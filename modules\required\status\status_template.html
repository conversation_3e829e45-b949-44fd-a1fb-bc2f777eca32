<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Status Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            padding: 20px;
            min-height: 100vh;
            position: relative;
            color: #000000;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 0;
        }

        .status-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .header {
            background: linear-gradient(135deg, rgba(255, 154, 86, 0.9) 0%, rgba(255, 107, 53, 0.9) 50%, rgba(247, 147, 30, 0.9) 100%);
            color: #000000;
            padding: 30px;
            text-align: center;
            position: relative;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .bot-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .bot-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .bot-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .bot-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #000000;
        }

        .bot-version {
            font-size: 14px;
            opacity: 0.9;
            color: #000000;
        }

        .uptime-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            color: #000000;
        }

        .uptime-info strong,
        .uptime-info span {
            color: #000000;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 25px;
        }



        .metric-card {
            background: rgba(255, 255, 255, 0.25);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 0.3);
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #000000;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .circular-metrics {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
        }

        .circular-progress {
            position: relative;
            width: 80px;
            height: 80px;
        }

        .progress-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(var(--progress-color) 0deg var(--progress-deg), #e0e0e0 var(--progress-deg) 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .progress-circle::before {
            content: '';
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            backdrop-filter: blur(10px);
        }

        .progress-text {
            position: absolute;
            font-size: 14px;
            font-weight: bold;
            color: #000000;
            z-index: 1;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .progress-label {
            text-align: center;
            margin-top: 10px;
            font-size: 12px;
            color: #000000;
            font-weight: bold;
        }

        .progress-detail {
            text-align: center;
            margin-top: 5px;
            font-size: 10px;
            color: #000000;
            opacity: 0.8;
        }

        .bar-chart {
            margin: 15px 0;
        }

        .bar-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .bar-label {
            width: 60px;
            font-size: 12px;
            font-weight: bold;
            color: #000000;
        }

        .bar-container {
            flex: 1;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            margin: 0 10px;
            overflow: hidden;
        }

        .bar-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .bar-value {
            font-size: 12px;
            font-weight: bold;
            color: #000000;
            min-width: 50px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-value {
            font-size: 24px;
            font-weight: bold;
            color: #000000;
            margin-bottom: 5px;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }

        .info-label {
            font-size: 12px;
            color: #000000;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
        }

        .message-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .message-item {
            text-align: center;
            flex: 1;
        }

        .message-count {
            font-size: 20px;
            font-weight: bold;
            color: #000000;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
        }

        .message-rate {
            font-size: 12px;
            color: #000000;
            margin-top: 5px;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .version-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 15px;
            margin-top: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .version-item {
            margin-bottom: 8px;
            font-size: 13px;
            color: #000000;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        .version-item:last-child {
            margin-bottom: 0;
        }



        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            color: #000000;
        }

        .status-online {
            background: #28a745;
        }

        .status-warning {
            background: #ffc107;
            color: #333;
        }

        .status-error {
            background: #dc3545;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #000000;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        }

        /* 图标样式 */
        .icon {
            width: 20px;
            height: 20px;
            display: inline-block;
        }

        .icon-cpu::before { content: '🖥️'; }
        .icon-memory::before { content: '💾'; }
        .icon-disk::before { content: '💿'; }
        .icon-network::before { content: '🌐'; }
        .icon-message::before { content: '💬'; }
        .icon-info::before { content: 'ℹ️'; }
    </style>
</head>
<body>
    <div class="status-container">
        <!-- Header Section -->
        <div class="header">
            <div class="bot-info">
                <div class="bot-avatar">
                    {% if avatar_base64 %}
                    <img src="data:image/jpeg;base64,{{ avatar_base64 }}" alt="小埋" onerror="this.style.display='none'; this.parentNode.innerHTML='埋';">
                    {% else %}
                    埋
                    {% endif %}
                </div>
                <div>
                    <div class="bot-name">{{ bot_name }}</div>
                    <div class="bot-version">{{ version_info }}</div>
                </div>
            </div>
            <div class="uptime-info">
                <div>
                    <strong>启动时间</strong><br>
                    <span>{{ launch_time }}</span>
                </div>
                <div>
                    <strong>运行时长</strong><br>
                    <span>{{ work_time }}</span>
                </div>
            </div>
        </div>

        <!-- Metrics Grid -->
        <div class="metrics-grid">
            <!-- System Resources -->
            <div class="metric-card">
                <div class="card-title">
                    <span class="icon icon-cpu"></span>
                    系统资源
                </div>
                <div class="circular-metrics">
                    <div class="circular-progress">
                        <div class="progress-circle" style="--progress-color: {{ cpu_color }}; --progress-deg: {{ cpu_deg }}deg;">
                            <div class="progress-text">{{ cpu_percent }}%</div>
                        </div>
                        <div class="progress-label">CPU</div>
                    </div>
                    <div class="circular-progress">
                        <div class="progress-circle" style="--progress-color: {{ memory_color }}; --progress-deg: {{ memory_deg }}deg;">
                            <div class="progress-text">{{ memory_percent }}%</div>
                        </div>
                        <div class="progress-label">内存</div>
                        <div class="progress-detail">{{ memory_used_mb }}MB / {{ memory_total_mb }}MB</div>
                    </div>
                    <div class="circular-progress">
                        <div class="progress-circle" style="--progress-color: {{ disk_color }}; --progress-deg: {{ disk_deg }}deg;">
                            <div class="progress-text">{{ disk_percent }}%</div>
                        </div>
                        <div class="progress-label">磁盘</div>
                        <div class="progress-detail">{{ disk_used_gb }}GB / {{ disk_total_gb }}GB</div>
                    </div>
                </div>
            </div>

            <!-- Bot状态与消息数据 -->
            <div class="metric-card">
                <div class="card-title">
                    <span class="icon icon-info"></span>
                    Bot状态与消息数据
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-value">{{ online_bots }}/{{ total_bots }}</div>
                        <div class="info-label">在线Bot数量</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ active_groups }}</div>
                        <div class="info-label">活动群组</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ received_count }}</div>
                        <div class="info-label">接收消息 ({{ real_time_received }}/分钟)</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ sent_count }}</div>
                        <div class="info-label">发送消息 ({{ real_time_sent }}/分钟)</div>
                    </div>
                </div>
            </div>

            <!-- Network Information -->
            <div class="metric-card">
                <div class="card-title">
                    <span class="icon icon-network"></span>
                    网络连接
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-value">{{ network_connections }}</div>
                        <div class="info-label">活动连接</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ network_packets_sent }}</div>
                        <div class="info-label">发送包数</div>
                    </div>
                </div>
                <div class="message-stats">
                    <div class="message-item">
                        <div class="message-count">{{ network_bytes_sent }}</div>
                        <div class="message-rate">发送流量</div>
                    </div>
                    <div class="message-item">
                        <div class="message-count">{{ network_bytes_recv }}</div>
                        <div class="message-rate">接收流量</div>
                    </div>
                </div>
            </div>

            <!-- Bot Process Information -->
            <div class="metric-card">
                <div class="card-title">
                    <span class="icon icon-cpu"></span>
                    Bot进程
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-value">{{ bot_pid }}</div>
                        <div class="info-label">进程ID</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ bot_threads }}</div>
                        <div class="info-label">线程数</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ bot_cpu_percent }}%</div>
                        <div class="info-label">CPU占用</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">{{ bot_memory_rss_mb }}MB</div>
                        <div class="info-label">内存占用({{ bot_memory_total_percent }}%)</div>
                    </div>
                </div>
            </div>

            <!-- Version Information -->
            <div class="metric-card">
                <div class="card-title">
                    <span class="icon icon-info"></span>
                    版本信息
                </div>
                <div class="version-info">
                    {% for item in version_details %}
                    <div class="version-item">{{ item }}</div>
                    {% endfor %}
                </div>
            </div>


        </div>

        <!-- Footer -->
        <div class="footer">
            项目地址：https://github.com/g1331/xiaomai-bot
        </div>
    </div>
</body>
</html>
