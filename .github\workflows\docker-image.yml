name: Docker Image CI/CD

on:
  push:
    branches: [ "v3" ]
  pull_request:
    branches: [ "v3" ]
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'

permissions:
  contents: read
  pull-requests: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Alibaba Cloud Container Registry (ACR) Login
        uses: aliyun/acr-login@v1.0.5
        with:
          login-server: https://registry.cn-hangzhou.aliyuncs.com
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Build and push image
        run: |
          docker build -t ${{ secrets.DOCKER_REPOSITORY }}:latest .
          docker push ${{ secrets.DOCKER_REPOSITORY }}:latest
  pull-docker:
    needs: [build]
    name: Pull Docker
    runs-on: ubuntu-latest
    steps:
      - name: Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.HOST_USERNAME }}
          password: ${{ secrets.HOST_PASSWORD }}
          port: ${{ secrets.HOST_PORT }}
          script: |
            sudo chmod 666 /var/run/docker.sock
            docker stop $(docker ps -a -q) || true
            docker rm $(docker ps -a -q) || true
            docker rmi $(docker images -q) || true
            docker login -u ${{ secrets.DOCKER_USERNAME }} -p ${{ secrets.DOCKER_PASSWORD }} registry.cn-hangzhou.aliyuncs.com
            docker pull ${{ secrets.DOCKER_REPOSITORY }}:latest
            docker run -p 3214:4000 -d ${{ secrets.DOCKER_REPOSITORY }}:latest
