version: '3'
services:
  xiaomai-bot:
    build:
      context: .
      dockerfile: Dockerfile
#    environment:
#      - bot_accounts=机器人账户使用,做分割
#      - default_account=默认机器人账户
#      - Master=机器人管理账户
#      - mirai_host=mirai服务器
#      - verify_key=mah验证token
#      - test_group=debug群组
#      - db_link=sqlite位置
    container_name: xiaomai-bot
    restart: always
    volumes:
      - ./config/config.yaml:/xiaomai-bot/config/config.yaml     # 配置文件
      - ./data.db:/xiaomai-bot/data.db                           # 数据库文件
      - ./data:/xiaomai-bot/data                                 # 数据目录
      - ./statics:/xiaomai-bot/statics                           # 静态资源目录
    network_mode: host