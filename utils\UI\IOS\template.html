<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/4.7.0/css/font-awesome.css">
    <style>

        *{margin: 0; padding: 0;}

        #main-box {
            width: {{ body_width }}px;
        }

        .light #main-box {
            background-color: rgb(242, 242, 246);
        }

        .dark #main-box {
            background-color: rgb(1, 1, 1);
        }

        .dark .column-item-title, .dark .column-item-list-item-subtitle, .dark .column-item-user-info-name  {
            color: white;
        }

        .dark .column-item-list, .dark .column-item-list-item, .dark .divider, .dark .column-item-user-info {
            background-color: rgb(28, 28, 29);
        }

        .flex {
            display: flex;
            justify-content: flex-start;
            align-content: center;
        }

        .flex-center {
            display: flex;
            justify-content: center;
            align-content: center;
        }

        .flex-column {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
        }

        .flex-space-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .column {
            padding: 20px;
            width: 540px;
        }

        .column-item {
            margin-bottom: 20px;
        }

        .column-item-title {
            width: 500px;
            font-size: 28px;
            padding: 5px;
        }

        .column-item-user-info {
            width: 500px;
            border-radius: 10px;
            padding: 20px;
            background: rgb(252, 252, 252);
        }

        .column-item-user-info-name {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .column-item-user-info-description {
            color: rgb(134, 134, 134);
        }

        .column-item-user-info-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }

        .column-item-user-info-avatar img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }

        .column-item-image, .column-item-image img {
            width: 540px;
            border-radius: 10px;
        }

        .column-item-list {
            width: 500px;
            background: white;
            border-radius: 10px;
        }

        .column-item-list-item {
            width: 500px;
            background: white;
            padding: 10px 20px;
        }

        .column-item-list .column-item-list-item:first-child {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .column-item-list .column-item-list-item:last-child {
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .column-item-list-item-subtitle {
            font-size: 24px;
        }

        .light .column-item-list-item-content {
            color: rgb(138, 138, 141);
        }

        .night .column-item-list-item-content {
            color: rgb(152, 152, 158);
        }

        .light hr {
            border: 1px solid rgb(200, 200, 202);
            width: 518px;
        }

        .light .text-with-item {
            color: rgb(138, 138, 141)
        }

        .light .mut-icon {
            color: rgb(196, 196, 198);
        }

        .dark hr {
            border: 1px solid rgb(60, 60, 63);
            width: 518px;
        }

        .dark .text-with-item {
            color: rgb(152, 152, 158)
        }

        .dark .mut-icon {
            color: rgb(90, 90, 94);
        }

        .divider {
            padding: 0 0 0 20px;
            width: 520px;
            background-color: white;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .dark .slider {
            background-color: rgb(57, 57, 61);
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: rgb(103, 206, 103);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px rgb(103, 206, 103);
        }

        input:checked + .slider:before {
            -webkit-transform: translateX(26px);
            -ms-transform: translateX(26px);
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .text-with-item {
            margin-right: 10px
        }

        .icon-box {
            border-radius: 5px;
            margin-right: 10px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="{{ color_type }}">
    <div id="main-box" class="flex">
        {% for column in columns %}
        <div class="column">
            {% for element in column.elements %}
            {% if element.el_name == "ColumnTitle" %}
            <div class="column-item column-item-title">
                <strong>{{ element.title }}</strong>
            </div>
            {% elif element.el_name == "ColumnUserInfo" %}
            <div class="column-item column-item-user-info flex-space-between">
                <div class="flex">
                    <div class="column-item-user-info-avatar">
                        <img src="{{ element.avatar }}">
                    </div>
                    <div style="padding-left: 20px; height: 100px;" class="flex-column">
                        <div class="column-item-user-info-name">{{ element.name }}</div>
                        <div class="column-item-user-info-description">{{ element.description }}</div>
                    </div>
                </div>
                <div>
                    <i class="mut-icon fa fa-chevron-right"></i>
                </div>
            </div>
            {% elif element.el_name == "ColumnImage" %}
            <div class="column-item column-item-image">
                <img src="{{ element.src }}">
            </div>
            {% elif element.el_name == "IOSColumnList" %}
            <div class="column-item column-item-list">
                {% for i in range(0, (element.rows | length)) %}
                <div class="column-item-list-item flex-space-between">
                    <div class="flex">
                        {% if element.rows[i].left_element %}
                            {% if element.rows[i].left_element.el_name == "IOSColumnListItemIcon" %}
                            <div class="icon-box" style="background-color: {{ element.rows[i].left_element.background_color }}; align-items: center">
                                <i class="fa {{ element.rows[i].left_element.awesome_font_name }}" style="color: {{ element.rows[i].left_element.icon_color }}; font-size: 24px"></i>
                            </div>
                            {% endif %}
                        {% endif %}
                        <div>
                            <div class="column-item-list-item-subtitle">{{ element.rows[i].subtitle }}</div>
                            <div class="column-item-list-item-content">{{ element.rows[i].content }}</div>
                        </div>
                    </div>
                    {% if element.rows[i].right_element %}
                    <div>
                        {% if element.rows[i].right_element.el_name == "ColumnListItemSwitch" %}
                        <label class="switch">
                            <input type="checkbox" {{ "checked" if element.rows[i].right_element.switch else "" }}>
                            <span class="slider round"></span>
                        </label>
                        {% elif element.rows[i].right_element.el_name == "ColumnListItemIcon" %}
                        <i class="mut-icon fa {{ element.rows[i].right_element.awesome_font_name }}"></i>
                        {% elif element.rows[i].right_element.el_name == "ColumnListTextWithItem" %}
                        <div class="flex">
                            <div class="text-with-item">
                                {{ element.rows[i].right_element.text }}
                            </div>
                            {% if element.rows[i].right_element.right_element %}
                                {% if element.rows[i].right_element.right_element.el_name == "ColumnListItemSwitch" %}
                                <label class="switch">
                                    <input type="checkbox" {{ "checked" if element.rows[i].right_element.right_element.switch else "" }}>
                                    <span class="slider round"></span>
                                </label>
                                {% elif element.rows[i].right_element.right_element.el_name == "ColumnListItemIcon" %}
                                <i class="mut-icon fa {{ element.rows[i].right_element.right_element.awesome_font_name }}"></i>
                                {% endif %}
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% if i != (element.rows | length) - 1 %}
                <div class="divider"><hr></div>
                {% endif %}
                {% endfor %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
        {% endfor %}
    </div>
</body>
</html>