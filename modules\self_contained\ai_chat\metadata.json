{"level": 1, "name": "AIChat", "version": "0.2", "display_name": "AI Chat", "authors": ["13"], "description": "AI Chat", "usage": ["指令:", "@bot 消息内容", "支持参数:", "-n / -new - 开启新对话", "-P / --pic - 以图片模式回复(默认文本回复)", "--no-tool - 禁用工具调用(支持工具调用的模型会默认开启)", "-p / -preset - 设定预设", "-c / --clear - 清除当前对话历史记录", "-m / --model - 切换模型", "-v / --provider - 切换AI服务提供商", "-r / --retry - 重试上次请求", "--show-preset - 显示预设", "--model-info / --info - 显示当前模型信息及状态", "--no-vision - 禁用多模态输入", "--reload-cfg - 重载配置(限BOT管理员)"], "example": ["@bot 你好", "@bot -n -p cat_girl 你好", "@bot -n -P 生成一段Python代码", "@bot --tool 讲讲关于CloseAI的消息", "@bot --show-preset", "@bot --info", "@bot --reload-cfg", "@bot -m deepseek-chat", "@bot -v cf-proxy-gemini"], "default_switch": false, "default_notice": true}