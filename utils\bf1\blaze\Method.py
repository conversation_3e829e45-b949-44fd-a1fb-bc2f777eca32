Components = {
    1: "Authentication",
    3: "Example",
    4: "GameManager",
    5: "Redirector",
    7: "Stats",
    9: "Util",
    10: "CensusData",
    11: "Clubs",
    15: "Messaging",
    25: "AssociationLists",
    27: "<PERSON><PERSON><PERSON><PERSON>ntController",
    28: "GameReporting",
    30: "Matchmaker",
    31: "ByteVault",
    33: "Achievements",
    334: "Greeter",
    401: "Search",
    402: "GameManagerMetrics",
    500: "GamePacker",
    1002: "NucleusIdentity",
    1003: "NucleusConnect",
    1006: "XBLProfile",
    1010: "Notify",
    1020: "XBLSocial",
    1021: "XBLPrivacyConfigs",
    1022: "XBLReputationConfigs",
    1023: "XBLReputationService",
    1024: "XBLServiceConfigs",
    1025: "XBLSystemConfigs",
    1026: "XBLClientSessionDirectory",
    1028: "ArsonXblSessionDirectoryArena",
    1029: "ArsonXBLOrganizerTournamentsHub",
    1030: "CCS",
    1031: "Friends",
    1035: "SecretVault",
    1037: "PAS",
    1099: "Recommendation",
    2001: "TestService",
    3000: "PSNBaseUrl",
    3001: "PSNSessionInvitation",
    3002: "PSNMatches",
    3003: "PSNSessionManager",
    3004: "PsnAuthZ",
    4000: "GdprCompliance",
    30722: "UserSessions",
}
Components2Int = {v: k for k, v in Components.items()}
Commands = {
    "Authentication": {
        "Command": {
            "10": "login",
            "11": "trustedLogin",
            "29": "listUserEntitlements2",
            "30": "getAccount",
            "31": "grantEntitlement",
            "32": "listEntitlements",
            "34": "getUseCount",
            "35": "decrementUseCount",
            "39": "grantEntitlement2",
            "43": "modifyEntitlement2",
            "47": "getPrivacyPolicyContent",
            "48": "listPersonaEntitlements2",
            "52": "getOptIn",
            "53": "enableOptIn",
            "54": "disableOptIn",
            "60": "expressLogin",
            "61": "stressLogin",
            "70": "logout",
            "90": "getPersona",
            "100": "listPersonas",
            "230": "createWalUserSession",
            "240": "checkLegalDoc",
            "242": "getEmailOptInSettings",
            "246": "getTermsOfServiceContent",
            "260": "getOriginPersona",
            "290": "guestLogin",
            "300": "getDecryptedBlazeIds",
        }
    },
    "GameManager": {
        "Command": {
            "1": "createGame",
            "2": "destroyGame",
            "3": "advanceGameState",
            "4": "setGameSettings",
            "5": "setPlayerCapacity",
            "6": "setPresenceMode",
            "7": "setGameAttributes",
            "8": "setPlayerAttributes",
            "9": "joinGame",
            "10": "createGameTemplate",
            "11": "removePlayer",
            "15": "finalizeGameCreation",
            "16": "startMatchmakingScenario",
            "17": "cancelMatchmakingScenario",
            "18": "setPlayerCustomData",
            "19": "replayGame",
            "20": "returnDedicatedServerToPool",
            "21": "setDedicatedServerAttributes",
            "22": "leaveGameByGroup",
            "23": "migrateGame",
            "24": "updateGameHostMigrationStatus",
            "25": "resetDedicatedServer",
            "26": "updateGameSession",
            "27": "banPlayer",
            "28": "matchmakingDedicatedServerOverride",
            "29": "updateMeshConnection",
            "30": "joinGameByUserList",
            "31": "removePlayerFromBannedList",
            "32": "clearBannedList",
            "33": "getBannedList",
            "34": "getMatchmakingDedicatedServerOverrides",
            "35": "matchmakingFillServersOverride",
            "36": "getMatchmakingFillServersOverride",
            "38": "addQueuedPlayerToGame",
            "39": "updateGameName",
            "40": "ejectHost",
            "41": "setGameModRegister",
            "42": "setGameEntryCriteria",
            "43": "preferredJoinOptOut",
            "44": "destroyGames",
            "45": "createOrJoinGame",
            "46": "requestPlatformHost",
            "47": "demoteReservedPlayerToQueue",
            "50": "createPseudoGame",
            "51": "createPseudoGames",
            "52": "cancelCreatePseudoGames",
            "53": "destroyAllPseudoGames",
            "54": "captureGameManagerEnvironment",
            "55": "isGameCaptureDone",
            "56": "getRedisDumpLocations",
            "65": "meshEndpointsConnected",
            "66": "meshEndpointsDisconnected",
            "67": "meshEndpointsConnectionLost",
            "68": "requestConnectivityViaCCS",
            "69": "freeConnectivityViaCCS",
            "70": "requestLeaseExtensionViaCCS",
            "90": "createTournamentGame",
            "91": "cancelTournamentGame",
            "92": "getTournamentGameStatus",
            "98": "getGameBrowserAttributesConfig",
            "99": "getGameListByScenario",
            "100": "getGameListSnapshot",
            "101": "getGameListSubscription",
            "102": "destroyGameList",
            "103": "getFullGameData",
            "104": "getMatchmakingConfig",
            "105": "getGameDataFromId",
            "106": "addAdminPlayer",
            "107": "removeAdminPlayer",
            "108": "getScenarioAttributesConfig",
            "109": "changeGameTeamId",
            "110": "migrateAdminPlayer",
            "111": "getUserSetGameListSubscription",
            "112": "swapPlayers",
            "113": "getGameDataByUser",
            "114": "getCreateGameTemplateAttributesConfig",
            "152": "getGameListSnapshotSync",
            "153": "findDedicatedServers",
            "171": "reportTelemetry",
            "172": "addUsersToConnectionMetricAudit",
            "173": "removeUsersFromConnectionMetricAudit",
            "174": "fetchAuditedUsers",
            "175": "fetchAuditedUserData",
            "176": "deleteUserAuditMetricData",
            "177": "updatePrimaryExternalSessionForUser",
            "178": "getScenarioDetails",
            "200": "startPseudoMatchmakingScenario",
            "201": "updateExternalSessionImage",
            "202": "updateExternalSessionStatus",
            "203": "setUserScenarioVariant",
            "204": "getScenarioVariants",
            "205": "clearUserScenarioVariant",
            "206": "getTemplatesAndAttributes",
            "207": "getScenariosAndAttributes",
        },
        "Message": {
            "3": "NotifyMatchmakingScenarioPseudoSuccess",
            "10": "NotifyMatchmakingFailed",
            "11": "NotifyMatchmakingSessionConnectionValidated",
            "12": "NotifyMatchmakingAsyncStatus",
            "13": "NotifyMatchmakingPseudoSuccess",
            "14": "NotifyRemoteMatchmakingStarted",
            "15": "NotifyRemoteMatchmakingEnded",
            "16": "NotifyGameRemoved",
            "20": "NotifyGameSetup",
            "21": "NotifyPlayerJoining",
            "22": "NotifyJoiningPlayerInitiateConnections",
            "23": "NotifyPlayerJoiningQueue",
            "24": "NotifyPlayerPromotedFromQueue",
            "25": "NotifyPlayerClaimingReservation",
            "26": "NotifyPlayerDemotedToQueue",
            "30": "NotifyPlayerJoinCompleted",
            "40": "NotifyPlayerRemoved",
            "41": "NotifyRemoteJoinFailed",
            "60": "NotifyHostMigrationFinished",
            "70": "NotifyHostMigrationStart",
            "71": "NotifyPlatformHostInitialized",
            "80": "NotifyGameAttribChange",
            "81": "NotifyDedicatedServerAttribChange",
            "90": "NotifyPlayerAttribChange",
            "95": "NotifyPlayerCustomDataChange",
            "100": "NotifyGameStateChange",
            "110": "NotifyGameSettingsChange",
            "111": "NotifyGameCapacityChange",
            "112": "NotifyGameReset",
            "113": "NotifyGameReportingIdChange",
            "114": "NotifyGamePresenceChanged",
            "115": "NotifyGameSessionUpdated",
            "116": "NotifyGamePlayerStateChange",
            "117": "NotifyGamePlayerTeamRoleSlotChange",
            "118": "NotifyGameTeamIdChange",
            "119": "NotifyProcessQueue",
            "120": "NotifyPresenceModeChanged",
            "121": "NotifyQueueChanged",
            "123": "NotifyGameModRegisterChanged",
            "124": "NotifyGameEntryCriteriaChanged",
            "125": "NotifyMatchmakingReservedExternalPlayers",
            "135": "NotifyHostedConnectivityAvailable",
            "201": "NotifyGameListUpdate",
            "202": "NotifyAdminListChange",
            "230": "NotifyGameNameChange",
            "1016": "NotifyGameRemoved",
            "1020": "NotifyGameSetup",
            "1021": "NotifyPlayerJoining",
            "1022": "NotifyJoiningPlayerInitiateConnections",
            "1023": "NotifyPlayerJoiningQueue",
            "1024": "NotifyPlayerPromotedFromQueue",
            "1025": "NotifyPlayerClaimingReservation",
            "1026": "NotifyPlayerDemotedToQueue",
            "1030": "NotifyPlayerJoinCompleted",
            "1040": "NotifyPlayerRemoved",
            "1041": "NotifyRemoteJoinFailed",
            "1060": "NotifyHostMigrationFinished",
            "1070": "NotifyHostMigrationStart",
            "1071": "NotifyPlatformHostInitialized",
            "1080": "NotifyGameAttribChange",
            "1081": "NotifyDedicatedServerAttribChange",
            "1090": "NotifyPlayerAttribChange",
            "1095": "NotifyPlayerCustomDataChange",
            "1100": "NotifyGameStateChange",
            "1110": "NotifyGameSettingsChange",
            "1111": "NotifyGameCapacityChange",
            "1112": "NotifyGameReset",
            "1113": "NotifyGameReportingIdChange",
            "1114": "NotifyGamePresenceChanged",
            "1115": "NotifyGameSessionUpdated",
            "1116": "NotifyGamePlayerStateChange",
            "1117": "NotifyGamePlayerTeamRoleSlotChange",
            "1118": "NotifyGameTeamIdChange",
            "1119": "NotifyProcessQueue",
            "1120": "NotifyPresenceModeChanged",
            "1121": "NotifyQueueChanged",
            "1123": "NotifyGameModRegisterChanged",
            "1124": "NotifyGameEntryCriteriaChanged",
            "1202": "NotifyAdminListChange",
            "1230": "NotifyGameNameChange",
            "1240": "NotifyMatchmakingFailed",
            "1250": "NotifyHostedConnectivityAvailable",
        },
    },
    "Redirector": {
        "Command": {
            "1": "getServerInstance",
            "7": "getServerAddressMap",
            "9": "getCACertificates",
            "10": "findCACertificates",
            "11": "publishServerInstanceInfo",
            "12": "updateServerInstanceInfo",
            "14": "getPeerServerAddress",
            "15": "getCAResolverMetrics",
            "16": "purgeServerRegistration",
            "17": "getServerInstanceHttp",
        }
    },
    "Stats": {
        "Command": {
            "1": "getStatDescs",
            "2": "getStats",
            "3": "getStatGroupList",
            "4": "getStatGroup",
            "5": "getStatsByGroup",
            "6": "getDateRange",
            "7": "getEntityCount",
            "8": "updateStats",
            "9": "wipeStats",
            "10": "getLeaderboardGroup",
            "11": "getLeaderboardFolderGroup",
            "12": "getLeaderboard",
            "13": "getCenteredLeaderboard",
            "14": "getFilteredLeaderboard",
            "15": "getKeyScopesMap",
            "16": "getStatsByGroupAsync",
            "162": "getStatsByGroupAsync2",
            "17": "getLeaderboardTreeAsync",
            "172": "getLeaderboardTreeAsync2",
            "18": "getLeaderboardEntityCount",
            "19": "getStatCategoryList",
            "20": "getPeriodIds",
            "21": "getLeaderboardRaw",
            "212": "getLeaderboardRaw2",
            "22": "getCenteredLeaderboardRaw",
            "222": "getCenteredLeaderboardRaw2",
            "23": "getFilteredLeaderboardRaw",
            "232": "getFilteredLeaderboardRaw2",
            "24": "changeKeyscopeValue",
            "25": "getEntityRank",
            "26": "initializeStatsTransaction",
            "27": "commitStatsTransaction",
            "28": "abortStatsTransaction",
            "29": "calcDerivedStats",
            "30": "retrieveValuesStats",
        },
        "Message": {
            "1": "UpdateCacheNotification",
            "2": "UpdateGlobalStatsNotification",
            "50": "GetStatsAsyncNotification",
            "51": "GetLeaderboardTreeNotification",
        },
    },
    "Util": {
        "Command": {
            "1": "fetchClientConfig",
            "2": "ping",
            "3": "setClientData",
            "4": "localizeStrings",
            "5": "getTelemetryServer",
            "6": "getTickerServer",
            "7": "preAuth",
            "8": "postAuth",
            "10": "userSettingsLoad",
            "11": "userSettingsSave",
            "12": "userSettingsLoadAll",
            "13": "userSettingsLoadAllForUserId",
            "14": "deleteUserSettings",
            "15": "userSettingsLoadMultiple",
            "20": "filterForProfanity",
            "21": "setClientMetrics",
            "23": "setConnectionState",
            "25": "getUserOptions",
            "26": "setUserOptions",
            "27": "suspendUserPing",
            "28": "setClientState",
            "29": "setClientUserMetrics",
        }
    },
    "Messaging": {
        "Command": {
            "1": "sendMessage",
            "2": "fetchMessages",
            "3": "purgeMessages",
            "4": "touchMessages",
            "5": "getMessages",
            "6": "sendSourceMessage",
            "7": "sendGlobalMessage",
        },
        "Message": {"1": "NotifyMessage"},
    },
    "AssociationLists": {
        "Command": {
            "1": "addUsersToList",
            "2": "removeUsersFromList",
            "3": "clearLists",
            "4": "setUsersToList",
            "5": "getListForUser",
            "6": "getLists",
            "7": "subscribeToLists",
            "8": "unsubscribeFromLists",
            "9": "getConfigListsInfo",
            "10": "getMemberHash",
            "11": "checkListMembership",
            "12": "checkListContainsMembers",
            "13": "setUsersAttributesInList",
        }
    },
    "GameReporting": {
        "Command": {
            "1": "submitGameReport",
            "100": "submitTrustedMidGameReport",
            "101": "submitTrustedEndGameReport",
            "2": "submitOfflineGameReport",
            "3": "submitGameEvents",
            "4": "getGameReportQuery",
            "5": "getGameReportQueriesList",
            "6": "getGameReports",
            "7": "getGameReportView",
            "8": "getGameReportViewInfo",
            "9": "getGameReportViewInfoList",
            "10": "getGameReportTypes",
            "11": "updateMetric",
            "12": "getGameReportColumnInfo",
            "13": "getGameReportColumnValues",
            "14": "getTournamentGameReportView",
            "200": "gameStarted",
            "201": "gameFinished",
        }
    },
    "Matchmaker": {
        "Command": {
            "1": "startMatchmakingInternal",
            "2": "cancelMatchmakingInternal",
            "10": "getMatchmakingConfig",
            "11": "getMatchmakingInstanceStatus",
            "33": "matchmakingDedicatedServerOverride",
            "34": "getMatchmakingDedicatedServerOverrides",
            "35": "matchmakingFillServersOverride",
            "36": "getMatchmakingFillServersOverride",
            "41": "gameSessionConnectionComplete",
            "42": "getMatchmakingMetrics",
        }
    },
    "Achievements": {
        "Command": {"1": "getAchievements", "3": "grantAchievement", "6": "postEvents"}
    },
    "Friends": {
        "Command": {"1": "muteUser", "2": "checkMuteStatus", "3": "unmuteUser"}
    },
    "UserSessions": {
        "Command": {
            "1": "validateSessionKey",
            "3": "fetchExtendedData",
            "5": "updateExtendedDataAttribute",
            "8": "updateHardwareFlags",
            "9": "getPermissions",
            "10": "getAccessGroup",
            "12": "lookupUser",
            "13": "lookupUsers",
            "14": "lookupUsersByPrefix",
            "15": "lookupUsersIdentification",
            "20": "updateNetworkInfo",
            "21": "listDefaultAccessGroup",
            "22": "listAuthorization",
            "23": "lookupUserGeoIPData",
            "24": "overrideUserGeoIPData",
            "25": "updateUserSessionClientData",
            "26": "setUserInfoAttribute",
            "27": "resetUserGeoIPData",
            "28": "lookupEntityByName",
            "29": "lookupEntityById",
            "30": "lookupEntitiesByIds",
            "31": "registerRemoteSlaveSession",
            "32": "lookupUserSessionId",
            "33": "fetchLastLocaleUsedAndAuthError",
            "34": "fetchUserFirstLastAuthTime",
            "36": "listAllPermissions",
            "37": "setUserGeoOptIn",
            "38": "remoteLoadUserExtendedData",
            "39": "remoteRefreshUserExtendedData",
            "40": "forceSessionLogout",
            "41": "enableUserAuditLogging",
            "42": "disableUserAuditLogging",
            "43": "requestUserExtendedDataProviderRegistration",
            "44": "getUserGeoIpData",
            "45": "getUniqueDeviceId",
            "46": "getIpFilterByName",
            "47": "lookupUsersByPrefixMultiNamespace",
            "48": "lookupUsersByPersonaNameMultiNamespace",
            "49": "lookupUsersByPersonaNamesMultiNamespace",
            "50": "lookupUsersByPersonaNames",
            "53": "checkConnectivity",
            "54": "forceOwnSessionLogout",
            "55": "updateLocalUserGroup",
            "100": "getUedInformationMap",
            "56": "fetchUsersAuditState",
            "57": "refreshQosPingSiteMap",
            "58": "getQosPingSites",
            "60": "setUserCrossPlatformOptIn",
            "61": "lookupUsersCrossPlatform",
            "62": "getDeviceLocality",
        },
        "Message": {
            "1": "UserSessionExtendedDataUpdate",
            "2": "UserAdded",
            "3": "UserRemoved",
            "5": "UserUpdated",
            "6": "UserSubscriptionsUpdated",
            "7": "SessionSubscriptionsUpdated",
            "8": "UserAuthenticated",
            "9": "UserUnauthenticated",
            "10": "NotifyUserInfoUpdated",
            "11": "NotifyQosSettingsUpdated",
        },
    },
}
flat_list = []
for cid, component in Components.items():
    if component in Commands:
        for command_type, command in Commands[component].items():
            flat_list += [
                [f"{component}.{name}", [int(cid), int(mid)]]
                for mid, name in command.items()
            ]
Methods = dict(flat_list)

Command2Hex = {
    "Authentication.login": "1.10",
    "Authentication.trustedLogin": "1.11",
    "Authentication.listUserEntitlements2": "1.29",
    "Authentication.getAccount": "1.30",
    "Authentication.grantEntitlement": "1.31",
    "Authentication.listEntitlements": "1.32",
    "Authentication.getUseCount": "1.34",
    "Authentication.decrementUseCount": "1.35",
    "Authentication.grantEntitlement2": "1.39",
    "Authentication.modifyEntitlement2": "1.43",
    "Authentication.getPrivacyPolicyContent": "1.47",
    "Authentication.listPersonaEntitlements2": "1.48",
    "Authentication.getOptIn": "1.52",
    "Authentication.enableOptIn": "1.53",
    "Authentication.disableOptIn": "1.54",
    "Authentication.expressLogin": "1.60",
    "Authentication.stressLogin": "1.61",
    "Authentication.logout": "1.70",
    "Authentication.getPersona": "1.90",
    "Authentication.listPersonas": "1.100",
    "Authentication.createWalUserSession": "1.230",
    "Authentication.checkLegalDoc": "1.240",
    "Authentication.getEmailOptInSettings": "1.242",
    "Authentication.getTermsOfServiceContent": "1.246",
    "Authentication.getOriginPersona": "1.260",
    "Authentication.guestLogin": "1.290",
    "Authentication.getDecryptedBlazeIds": "1.300",
    "GameManager.createGame": "4.1",
    "GameManager.destroyGame": "4.2",
    "GameManager.advanceGameState": "4.3",
    "GameManager.setGameSettings": "4.4",
    "GameManager.setPlayerCapacity": "4.5",
    "GameManager.setPresenceMode": "4.6",
    "GameManager.setGameAttributes": "4.7",
    "GameManager.setPlayerAttributes": "4.8",
    "GameManager.joinGame": "4.9",
    "GameManager.createGameTemplate": "4.10",
    "GameManager.removePlayer": "4.11",
    "GameManager.finalizeGameCreation": "4.15",
    "GameManager.startMatchmakingScenario": "4.16",
    "GameManager.cancelMatchmakingScenario": "4.17",
    "GameManager.setPlayerCustomData": "4.18",
    "GameManager.replayGame": "4.19",
    "GameManager.returnDedicatedServerToPool": "4.20",
    "GameManager.setDedicatedServerAttributes": "4.21",
    "GameManager.leaveGameByGroup": "4.22",
    "GameManager.migrateGame": "4.23",
    "GameManager.updateGameHostMigrationStatus": "4.24",
    "GameManager.resetDedicatedServer": "4.25",
    "GameManager.updateGameSession": "4.26",
    "GameManager.banPlayer": "4.27",
    "GameManager.matchmakingDedicatedServerOverride": "4.28",
    "GameManager.updateMeshConnection": "4.29",
    "GameManager.joinGameByUserList": "4.30",
    "GameManager.removePlayerFromBannedList": "4.31",
    "GameManager.clearBannedList": "4.32",
    "GameManager.getBannedList": "4.33",
    "GameManager.getMatchmakingDedicatedServerOverrides": "4.34",
    "GameManager.matchmakingFillServersOverride": "4.35",
    "GameManager.getMatchmakingFillServersOverride": "4.36",
    "GameManager.addQueuedPlayerToGame": "4.38",
    "GameManager.updateGameName": "4.39",
    "GameManager.ejectHost": "4.40",
    "GameManager.setGameModRegister": "4.41",
    "GameManager.setGameEntryCriteria": "4.42",
    "GameManager.preferredJoinOptOut": "4.43",
    "GameManager.destroyGames": "4.44",
    "GameManager.createOrJoinGame": "4.45",
    "GameManager.requestPlatformHost": "4.46",
    "GameManager.demoteReservedPlayerToQueue": "4.47",
    "GameManager.createPseudoGame": "4.50",
    "GameManager.createPseudoGames": "4.51",
    "GameManager.cancelCreatePseudoGames": "4.52",
    "GameManager.destroyAllPseudoGames": "4.53",
    "GameManager.captureGameManagerEnvironment": "4.54",
    "GameManager.isGameCaptureDone": "4.55",
    "GameManager.getRedisDumpLocations": "4.56",
    "GameManager.meshEndpointsConnected": "4.65",
    "GameManager.meshEndpointsDisconnected": "4.66",
    "GameManager.meshEndpointsConnectionLost": "4.67",
    "GameManager.requestConnectivityViaCCS": "4.68",
    "GameManager.freeConnectivityViaCCS": "4.69",
    "GameManager.requestLeaseExtensionViaCCS": "4.70",
    "GameManager.createTournamentGame": "4.90",
    "GameManager.cancelTournamentGame": "4.91",
    "GameManager.getTournamentGameStatus": "4.92",
    "GameManager.getGameBrowserAttributesConfig": "4.98",
    "GameManager.getGameListByScenario": "4.99",
    "GameManager.getGameListSnapshot": "4.100",
    "GameManager.getGameListSubscription": "4.101",
    "GameManager.destroyGameList": "4.102",
    "GameManager.getFullGameData": "4.103",
    "GameManager.getMatchmakingConfig": "4.104",
    "GameManager.getGameDataFromId": "4.105",
    "GameManager.addAdminPlayer": "4.106",
    "GameManager.removeAdminPlayer": "4.107",
    "GameManager.getScenarioAttributesConfig": "4.108",
    "GameManager.changeGameTeamId": "4.109",
    "GameManager.migrateAdminPlayer": "4.110",
    "GameManager.getUserSetGameListSubscription": "4.111",
    "GameManager.swapPlayers": "4.112",
    "GameManager.getGameDataByUser": "4.113",
    "GameManager.getCreateGameTemplateAttributesConfig": "4.114",
    "GameManager.getGameListSnapshotSync": "4.152",
    "GameManager.findDedicatedServers": "4.153",
    "GameManager.reportTelemetry": "4.171",
    "GameManager.addUsersToConnectionMetricAudit": "4.172",
    "GameManager.removeUsersFromConnectionMetricAudit": "4.173",
    "GameManager.fetchAuditedUsers": "4.174",
    "GameManager.fetchAuditedUserData": "4.175",
    "GameManager.deleteUserAuditMetricData": "4.176",
    "GameManager.updatePrimaryExternalSessionForUser": "4.177",
    "GameManager.getScenarioDetails": "4.178",
    "GameManager.startPseudoMatchmakingScenario": "4.200",
    "GameManager.updateExternalSessionImage": "4.201",
    "GameManager.updateExternalSessionStatus": "4.202",
    "GameManager.setUserScenarioVariant": "4.203",
    "GameManager.getScenarioVariants": "4.204",
    "GameManager.clearUserScenarioVariant": "4.205",
    "GameManager.getTemplatesAndAttributes": "4.206",
    "GameManager.getScenariosAndAttributes": "4.207",
    "Redirector.getServerInstance": "5.1",
    "Redirector.getServerAddressMap": "5.7",
    "Redirector.getCACertificates": "5.9",
    "Redirector.findCACertificates": "5.10",
    "Redirector.publishServerInstanceInfo": "5.11",
    "Redirector.updateServerInstanceInfo": "5.12",
    "Redirector.getPeerServerAddress": "5.14",
    "Redirector.getCAResolverMetrics": "5.15",
    "Redirector.purgeServerRegistration": "5.16",
    "Redirector.getServerInstanceHttp": "5.17",
    "Stats.getStatDescs": "7.1",
    "Stats.getStats": "7.2",
    "Stats.getStatGroupList": "7.3",
    "Stats.getStatGroup": "7.4",
    "Stats.getStatsByGroup": "7.5",
    "Stats.getDateRange": "7.6",
    "Stats.getEntityCount": "7.7",
    "Stats.updateStats": "7.8",
    "Stats.wipeStats": "7.9",
    "Stats.getLeaderboardGroup": "7.10",
    "Stats.getLeaderboardFolderGroup": "7.11",
    "Stats.getLeaderboard": "7.12",
    "Stats.getCenteredLeaderboard": "7.13",
    "Stats.getFilteredLeaderboard": "7.14",
    "Stats.getKeyScopesMap": "7.15",
    "Stats.getStatsByGroupAsync": "7.16",
    "Stats.getLeaderboardTreeAsync": "7.17",
    "Stats.getLeaderboardEntityCount": "7.18",
    "Stats.getStatCategoryList": "7.19",
    "Stats.getPeriodIds": "7.20",
    "Stats.getLeaderboardRaw": "7.21",
    "Stats.getCenteredLeaderboardRaw": "7.22",
    "Stats.getFilteredLeaderboardRaw": "7.23",
    "Stats.changeKeyscopeValue": "7.24",
    "Stats.getEntityRank": "7.25",
    "Stats.initializeStatsTransaction": "7.26",
    "Stats.commitStatsTransaction": "7.27",
    "Stats.abortStatsTransaction": "7.28",
    "Stats.calcDerivedStats": "7.29",
    "Stats.retrieveValuesStats": "7.30",
    "Stats.getStatsByGroupAsync2": "7.162",
    "Stats.getLeaderboardTreeAsync2": "7.172",
    "Stats.getLeaderboardRaw2": "7.212",
    "Stats.getCenteredLeaderboardRaw2": "7.222",
    "Stats.getFilteredLeaderboardRaw2": "7.232",
    "Util.fetchClientConfig": "9.1",
    "Util.ping": "9.2",
    "Util.setClientData": "9.3",
    "Util.localizeStrings": "9.4",
    "Util.getTelemetryServer": "9.5",
    "Util.getTickerServer": "9.6",
    "Util.preAuth": "9.7",
    "Util.postAuth": "9.8",
    "Util.userSettingsLoad": "9.10",
    "Util.userSettingsSave": "9.11",
    "Util.userSettingsLoadAll": "9.12",
    "Util.userSettingsLoadAllForUserId": "9.13",
    "Util.deleteUserSettings": "9.14",
    "Util.userSettingsLoadMultiple": "9.15",
    "Util.filterForProfanity": "9.20",
    "Util.setClientMetrics": "9.21",
    "Util.setConnectionState": "9.23",
    "Util.getUserOptions": "9.25",
    "Util.setUserOptions": "9.26",
    "Util.suspendUserPing": "9.27",
    "Util.setClientState": "9.28",
    "Util.setClientUserMetrics": "9.29",
    "Messaging.sendMessage": "15.1",
    "Messaging.fetchMessages": "15.2",
    "Messaging.purgeMessages": "15.3",
    "Messaging.touchMessages": "15.4",
    "Messaging.getMessages": "15.5",
    "Messaging.sendSourceMessage": "15.6",
    "Messaging.sendGlobalMessage": "15.7",
    "AssociationLists.addUsersToList": "25.1",
    "AssociationLists.removeUsersFromList": "25.2",
    "AssociationLists.clearLists": "25.3",
    "AssociationLists.setUsersToList": "25.4",
    "AssociationLists.getListForUser": "25.5",
    "AssociationLists.getLists": "25.6",
    "AssociationLists.subscribeToLists": "25.7",
    "AssociationLists.unsubscribeFromLists": "25.8",
    "AssociationLists.getConfigListsInfo": "25.9",
    "AssociationLists.getMemberHash": "25.10",
    "AssociationLists.checkListMembership": "25.11",
    "AssociationLists.checkListContainsMembers": "25.12",
    "AssociationLists.setUsersAttributesInList": "25.13",
    "GameReporting.submitGameReport": "28.1",
    "GameReporting.submitOfflineGameReport": "28.2",
    "GameReporting.submitGameEvents": "28.3",
    "GameReporting.getGameReportQuery": "28.4",
    "GameReporting.getGameReportQueriesList": "28.5",
    "GameReporting.getGameReports": "28.6",
    "GameReporting.getGameReportView": "28.7",
    "GameReporting.getGameReportViewInfo": "28.8",
    "GameReporting.getGameReportViewInfoList": "28.9",
    "GameReporting.getGameReportTypes": "28.10",
    "GameReporting.updateMetric": "28.11",
    "GameReporting.getGameReportColumnInfo": "28.12",
    "GameReporting.getGameReportColumnValues": "28.13",
    "GameReporting.getTournamentGameReportView": "28.14",
    "GameReporting.submitTrustedMidGameReport": "28.100",
    "GameReporting.submitTrustedEndGameReport": "28.101",
    "GameReporting.gameStarted": "28.200",
    "GameReporting.gameFinished": "28.201",
    "Matchmaker.startMatchmakingInternal": "30.1",
    "Matchmaker.cancelMatchmakingInternal": "30.2",
    "Matchmaker.getMatchmakingConfig": "30.10",
    "Matchmaker.getMatchmakingInstanceStatus": "30.11",
    "Matchmaker.matchmakingDedicatedServerOverride": "30.33",
    "Matchmaker.getMatchmakingDedicatedServerOverrides": "30.34",
    "Matchmaker.matchmakingFillServersOverride": "30.35",
    "Matchmaker.getMatchmakingFillServersOverride": "30.36",
    "Matchmaker.gameSessionConnectionComplete": "30.41",
    "Matchmaker.getMatchmakingMetrics": "30.42",
    "Achievements.getAchievements": "33.1",
    "Achievements.grantAchievement": "33.3",
    "Achievements.postEvents": "33.6",
    "Friends.muteUser": "1031.1",
    "Friends.checkMuteStatus": "1031.2",
    "Friends.unmuteUser": "1031.3",
    "UserSessions.validateSessionKey": "30722.1",
    "UserSessions.fetchExtendedData": "30722.3",
    "UserSessions.updateExtendedDataAttribute": "30722.5",
    "UserSessions.updateHardwareFlags": "30722.8",
    "UserSessions.getPermissions": "30722.9",
    "UserSessions.getAccessGroup": "30722.10",
    "UserSessions.lookupUser": "30722.12",
    "UserSessions.lookupUsers": "30722.13",
    "UserSessions.lookupUsersByPrefix": "30722.14",
    "UserSessions.lookupUsersIdentification": "30722.15",
    "UserSessions.updateNetworkInfo": "30722.20",
    "UserSessions.listDefaultAccessGroup": "30722.21",
    "UserSessions.listAuthorization": "30722.22",
    "UserSessions.lookupUserGeoIPData": "30722.23",
    "UserSessions.overrideUserGeoIPData": "30722.24",
    "UserSessions.updateUserSessionClientData": "30722.25",
    "UserSessions.setUserInfoAttribute": "30722.26",
    "UserSessions.resetUserGeoIPData": "30722.27",
    "UserSessions.lookupEntityByName": "30722.28",
    "UserSessions.lookupEntityById": "30722.29",
    "UserSessions.lookupEntitiesByIds": "30722.30",
    "UserSessions.registerRemoteSlaveSession": "30722.31",
    "UserSessions.lookupUserSessionId": "30722.32",
    "UserSessions.fetchLastLocaleUsedAndAuthError": "30722.33",
    "UserSessions.fetchUserFirstLastAuthTime": "30722.34",
    "UserSessions.listAllPermissions": "30722.36",
    "UserSessions.setUserGeoOptIn": "30722.37",
    "UserSessions.remoteLoadUserExtendedData": "30722.38",
    "UserSessions.remoteRefreshUserExtendedData": "30722.39",
    "UserSessions.forceSessionLogout": "30722.40",
    "UserSessions.enableUserAuditLogging": "30722.41",
    "UserSessions.disableUserAuditLogging": "30722.42",
    "UserSessions.requestUserExtendedDataProviderRegistration": "30722.43",
    "UserSessions.getUserGeoIpData": "30722.44",
    "UserSessions.getUniqueDeviceId": "30722.45",
    "UserSessions.getIpFilterByName": "30722.46",
    "UserSessions.lookupUsersByPrefixMultiNamespace": "30722.47",
    "UserSessions.lookupUsersByPersonaNameMultiNamespace": "30722.48",
    "UserSessions.lookupUsersByPersonaNamesMultiNamespace": "30722.49",
    "UserSessions.lookupUsersByPersonaNames": "30722.50",
    "UserSessions.checkConnectivity": "30722.53",
    "UserSessions.forceOwnSessionLogout": "30722.54",
    "UserSessions.updateLocalUserGroup": "30722.55",
    "UserSessions.fetchUsersAuditState": "30722.56",
    "UserSessions.refreshQosPingSiteMap": "30722.57",
    "UserSessions.getQosPingSites": "30722.58",
    "UserSessions.setUserCrossPlatformOptIn": "30722.60",
    "UserSessions.lookupUsersCrossPlatform": "30722.61",
    "UserSessions.getDeviceLocality": "30722.62",
    "UserSessions.getUedInformationMap": "30722.100",
}

Hex2Command = {v: k for k, v in Command2Hex.items()}

Message2Hex = {
    "GameManager.NotifyMatchmakingScenarioPseudoSuccess": "4.3",
    "GameManager.NotifyMatchmakingFailed": "4.1240",
    "GameManager.NotifyMatchmakingSessionConnectionValidated": "4.11",
    "GameManager.NotifyMatchmakingAsyncStatus": "4.12",
    "GameManager.NotifyMatchmakingPseudoSuccess": "4.13",
    "GameManager.NotifyRemoteMatchmakingStarted": "4.14",
    "GameManager.NotifyRemoteMatchmakingEnded": "4.15",
    "GameManager.NotifyGameRemoved": "4.1016",
    "GameManager.NotifyGameSetup": "4.1020",
    "GameManager.NotifyPlayerJoining": "4.1021",
    "GameManager.NotifyJoiningPlayerInitiateConnections": "4.1022",
    "GameManager.NotifyPlayerJoiningQueue": "4.1023",
    "GameManager.NotifyPlayerPromotedFromQueue": "4.1024",
    "GameManager.NotifyPlayerClaimingReservation": "4.1025",
    "GameManager.NotifyPlayerDemotedToQueue": "4.1026",
    "GameManager.NotifyPlayerJoinCompleted": "4.1030",
    "GameManager.NotifyPlayerRemoved": "4.1040",
    "GameManager.NotifyRemoteJoinFailed": "4.1041",
    "GameManager.NotifyHostMigrationFinished": "4.1060",
    "GameManager.NotifyHostMigrationStart": "4.1070",
    "GameManager.NotifyPlatformHostInitialized": "4.1071",
    "GameManager.NotifyGameAttribChange": "4.1080",
    "GameManager.NotifyDedicatedServerAttribChange": "4.1081",
    "GameManager.NotifyPlayerAttribChange": "4.1090",
    "GameManager.NotifyPlayerCustomDataChange": "4.1095",
    "GameManager.NotifyGameStateChange": "4.1100",
    "GameManager.NotifyGameSettingsChange": "4.1110",
    "GameManager.NotifyGameCapacityChange": "4.1111",
    "GameManager.NotifyGameReset": "4.1112",
    "GameManager.NotifyGameReportingIdChange": "4.1113",
    "GameManager.NotifyGamePresenceChanged": "4.1114",
    "GameManager.NotifyGameSessionUpdated": "4.1115",
    "GameManager.NotifyGamePlayerStateChange": "4.1116",
    "GameManager.NotifyGamePlayerTeamRoleSlotChange": "4.1117",
    "GameManager.NotifyGameTeamIdChange": "4.1118",
    "GameManager.NotifyProcessQueue": "4.1119",
    "GameManager.NotifyPresenceModeChanged": "4.1120",
    "GameManager.NotifyQueueChanged": "4.1121",
    "GameManager.NotifyGameModRegisterChanged": "4.1123",
    "GameManager.NotifyGameEntryCriteriaChanged": "4.1124",
    "GameManager.NotifyMatchmakingReservedExternalPlayers": "4.125",
    "GameManager.NotifyHostedConnectivityAvailable": "4.1250",
    "GameManager.NotifyGameListUpdate": "4.201",
    "GameManager.NotifyAdminListChange": "4.1202",
    "GameManager.NotifyGameNameChange": "4.1230",
    "Stats.UpdateCacheNotification": "7.1",
    "Stats.UpdateGlobalStatsNotification": "7.2",
    "Stats.GetStatsAsyncNotification": "7.50",
    "Stats.GetLeaderboardTreeNotification": "7.51",
    "Messaging.NotifyMessage": "15.1",
    "UserSessions.UserSessionExtendedDataUpdate": "30722.1",
    "UserSessions.UserAdded": "30722.2",
    "UserSessions.UserRemoved": "30722.3",
    "UserSessions.UserUpdated": "30722.5",
    "UserSessions.UserSubscriptionsUpdated": "30722.6",
    "UserSessions.SessionSubscriptionsUpdated": "30722.7",
    "UserSessions.UserAuthenticated": "30722.8",
    "UserSessions.UserUnauthenticated": "30722.9",
    "UserSessions.NotifyUserInfoUpdated": "30722.10",
    "UserSessions.NotifyQosSettingsUpdated": "30722.11",
}

Hex2Message = {v: k for k, v in Message2Hex.items()}

all_keys = list(Command2Hex.keys()) + list(Message2Hex.keys()) + ["KeepAlive"]
Method = tuple(all_keys)

ErrorName = {
    0.1: "ERR_SYSTEM",
    0.2: "ERR_COMPONENT_NOT_FOUND",
    0.3: "ERR_COMMAND_NOT_FOUND",
    0.4: "ERR_AUTHENTICATION_REQUIRED",
    0.5: "ERR_TIMEOUT",
    0.6: "ERR_DISCONNECTED",
    0.7: "ERR_DUPLICATE_LOGIN",
    0.8: "ERR_AUTHORIZATION_REQUIRED",
    0.9: "ERR_CANCELED",
    "0.10": "ERR_CUSTOM_REQUEST_HOOK_FAILED",
    0.11: "ERR_CUSTOM_RESPONSE_HOOK_FAILED",
    0.12: "ERR_TDF_STRING_TOO_LONG",
    0.13: "ERR_INVALID_TDF_ENUM_VALUE",
    0.14: "ERR_MOVED",
    0.16: "ERR_COMMAND_FIBERS_MAXED_OUT",
    0.17: "ERR_INVALID_ENDPOINT",
    0.18: "ERR_TDF_STRING_NOT_UTF8",
    0.19: "ERR_UNAVAILABLE",
    "0.20": "ERR_NOT_PRIMARY_PERSONA",
    0.101: "ERR_DB_SYSTEM",
    0.102: "ERR_DB_NOT_CONNECTED",
    0.103: "ERR_DB_NOT_SUPPORTED",
    0.104: "ERR_DB_NO_CONNECTION_AVAILABLE",
    0.105: "ERR_DB_DUP_ENTRY",
    0.106: "ERR_DB_NO_SUCH_TABLE",
    0.107: "ERR_DB_DISCONNECTED",
    0.108: "ERR_DB_TIMEOUT",
    0.109: "ERR_DB_INIT_FAILED",
    "0.110": "ERR_DB_TRANSACTION_NOT_COMPLETE",
    0.111: "ERR_DB_LOCK_DEADLOCK",
    0.112: "ERR_DB_DROP_PARTITION_NON_EXISTENT",
    0.113: "ERR_DB_SAME_NAME_PARTITION",
    0.114: "ERR_SERVER_BUSY",
    0.115: "ERR_GUEST_SESSION_NOT_ALLOWED",
    0.116: "ERR_DB_USER_DEFINED_EXCEPTION",
    0.117: "ERR_COULDNT_CONNECT",
    0.118: "ERR_DB_PREVIOUS_TRANSACTION_IN_PROGRESS",
    0.119: "ERR_DB_NO_ROWS_AFFECTED",
    "0.120": "ERR_DB_NO_SUCH_THREAD",
    1.1: "AUTH_ERR_INVALID_TOKEN",
    1.2: "AUTH_ERR_INVALID_REQUEST",
    1.3: "AUTH_ERR_INVALID_AUTHCODE",
    1.4: "AUTH_ERR_INVALID_PLATFORM",
    1.6: "AUTH_ERR_INVALID_SANDBOX_ID",
    "1.10": "AUTH_ERR_INVALID_COUNTRY",
    1.11: "AUTH_ERR_INVALID_USER",
    1.12: "AUTH_ERR_INVALID_PASSWORD",
    1.14: "AUTH_ERR_EXPIRED_TOKEN",
    1.15: "AUTH_ERR_EXISTS",
    1.16: "AUTH_ERR_TOO_YOUNG",
    1.17: "AUTH_ERR_NO_ACCOUNT",
    1.18: "AUTH_ERR_PERSONA_NOT_FOUND",
    1.19: "AUTH_ERR_PERSONA_INACTIVE",
    "1.20": "AUTH_ERR_INVALID_PMAIL",
    1.21: "AUTH_ERR_INVALID_FIELD",
    1.22: "AUTH_ERR_INVALID_EMAIL",
    1.23: "AUTH_ERR_INVALID_STATUS",
    1.32: "AUTH_ERR_PERSONA_BANNED",
    1.33: "AUTH_ERR_INVALID_PERSONA",
    1.34: "AUTH_ERR_CURRENT_PASSWORD_REQUIRED",
    1.41: "AUTH_ERR_DEACTIVATED",
    1.43: "AUTH_ERR_BANNED",
    1.44: "AUTH_ERR_DISABLED",
    1.45: "AUTH_ERR_DUPLICATE_LOGIN",
    1.46: "AUTH_ERR_RESTRICTION_VIOLATION",
    1.47: "AUTH_ERR_MULTIPLE_WALLET_ACCOUNTS_FOUND",
    1.48: "AUTH_ERR_INVALID_PRODUCT_CONFIGURATION",
    1.49: "AUTH_ERR_ENTITLEMENT_TAG_MISSING",
    "1.50": "AUTH_ERR_BAD_GATEWAY",
    1.54: "AUTH_ERR_NO_ASSOCIATED_PRODUCT",
    1.55: "AUTH_ERR_INVALID_MAPPING_ERROR",
    1.56: "AUTH_ERR_NO_SUCH_GROUP_NAME",
    1.57: "AUTH_ERR_MISSING_PERSONAID",
    1.58: "AUTH_ERR_USER_DOES_NOT_MATCH_PERSONA",
    1.59: "AUTH_ERR_WHITELIST",
    "1.60": "AUTH_ERR_LINK_PERSONA",
    1.61: "AUTH_ERR_NO_SUCH_GROUP",
    1.63: "AUTH_ERR_NO_SUCH_ENTITLEMENT",
    1.66: "AUTH_ERR_USECOUNT_ZERO",
    1.67: "AUTH_ERR_ENTITLEMETNTAG_EMPTY",
    "1.70": "AUTH_ERR_GROUPNAME_REQUIRED",
    1.71: "AUTH_ERR_GROUPNAME_INVALID",
    1.72: "AUTH_ERR_TOO_MANY_ENTITLEMENTS",
    1.73: "AUTH_ERR_PAGESIZE_ZERO",
    1.74: "AUTH_ERR_ENTITLEMENT_TAG_REQUIRED",
    1.75: "AUTH_ERR_PAGENO_ZERO",
    1.76: "AUTH_ERR_MODIFIED_STATUS_INVALID",
    1.77: "AUTH_ERR_USECOUNT_INCREMENT",
    1.78: "AUTH_ERR_TERMINATION_INVALID",
    1.79: "AUTH_ERR_UNKNOWN_ENTITLEMENT",
    "1.80": "AUTH_ERR_EXCEEDS_PSU_LIMIT",
    1.81: "AUTH_ERR_OPTIN_NAME_REQUIRED",
    1.82: "AUTH_ERR_INVALID_OPTIN",
    1.83: "AUTH_ERR_OPTIN_MISMATCH",
    1.84: "AUTH_ERR_NO_SUCH_OPTIN",
    1.85: "AUTH_ERR_AUTHID_REQUIRED",
    1.86: "AUTH_ERR_PERSONA_EXTREFID_REQUIRED",
    1.87: "AUTH_ERR_SOURCE_REQUIRED",
    1.88: "AUTH_ERR_APPLICATION_REQUIRED",
    1.89: "AUTH_ERR_TOKEN_REQUIRED",
    "1.90": "AUTH_ERR_PARAMETER_TOO_LENGTH",
    1.91: "AUTH_ERR_NO_SUCH_PERSONA_REFERENCE",
    1.93: "AUTH_ERR_INVALID_SOURCE",
    1.94: "AUTH_ERR_NO_SUCH_AUTH_DATA",
    1.95: "AUTH_ERR_NOT_PRIMARY_PERSONA",
    1.101: "AUTH_ERR_USER_INACTIVE",
    1.102: "AUTH_ERR_UNEXPECTED_ACTIVATION",
    1.103: "AUTH_ERR_NAME_MISMATCH",
    1.105: "AUTH_ERR_INVALID_NAMESPACE",
    1.198: "AUTH_ERR_FIELD_MIN_LOWER_CHARS",
    1.199: "AUTH_ERR_FIELD_MIN_UPPER_CHARS",
    "1.200": "AUTH_ERR_FIELD_MIN_DIGITS",
    1.201: "AUTH_ERR_FIELD_INVALID_CHARS",
    1.202: "AUTH_ERR_FIELD_TOO_SHORT",
    1.204: "AUTH_ERR_FIELD_MUST_BEGIN_WITH_LETTER",
    1.205: "AUTH_ERR_FIELD_MISSING",
    1.206: "AUTH_ERR_FIELD_INVALID",
    1.207: "AUTH_ERR_FIELD_NOT_ALLOWED",
    1.208: "AUTH_ERR_FIELD_NEEDS_SPECIAL_CHARS",
    1.209: "AUTH_ERR_FIELD_ALREADY_EXISTS",
    "1.210": "AUTH_ERR_FIELD_NEEDS_CONSENT",
    1.211: "AUTH_ERR_FIELD_TOO_YOUNG",
    1.212: "AUTH_ERR_ASSOCIATION_TOO_YOUNG",
    "1.300": "AUTH_ERR_TOO_MANY_PERSONA_FOR_NAMESPACE",
    1.303: "AUTH_ERR_NEEDS_PMAIL",
    1.307: "AUTH_ERR_NO_PARENT_SESSION",
    1.308: "AUTH_ERR_NO_XBLTOKEN",
    1.309: "AUTH_ERR_NO_PSNTOKEN",
    "1.310": "AUTH_ERR_TRIAL_PERIOD_CLOSED",
    1.311: "AUTH_ERR_EXPIRED_1PTOKEN",
    4.1: "GAMEMANAGER_ERR_INVALID_GAME_SETTINGS",
    4.2: "GAMEMANAGER_ERR_INVALID_GAME_ID",
    4.3: "GAMEMANAGER_ERR_JOIN_METHOD_NOT_SUPPORTED",
    4.4: "GAMEMANAGER_ERR_PARTICIPANT_SLOTS_FULL",
    4.5: "GAMEMANAGER_ERR_INVALID_GAME_STATE_TRANSITION",
    4.6: "GAMEMANAGER_ERR_INVALID_GAME_STATE_ACTION",
    4.7: "GAMEMANAGER_ERR_FAILED_IN_GAME_DESTROY",
    4.8: "GAMEMANAGER_ERR_QUEUE_FULL",
    4.9: "GAMEMANAGER_ERR_INVALID_GAME_ENTRY_CRITERIA",
    "4.10": "GAMEMANAGER_ERR_GAME_PROTOCOL_VERSION_MISMATCH",
    4.11: "GAMEMANAGER_ERR_GAME_IN_PROGRESS",
    4.12: "GAMEMANAGER_ERR_RESERVED_GAME_ID_INVALID",
    4.13: "GAMEMANAGER_ERR_INVALID_JOIN_METHOD",
    4.14: "GAMEMANAGER_ERR_SLOT_OCCUPIED",
    4.15: "GAMEMANAGER_ERR_NOT_VIRTUAL_GAME",
    4.16: "GAMEMANAGER_ERR_NOT_TOPOLOGY_HOST",
    4.17: "GAMEMANAGER_ERR_TOPOLOGY_NOT_SUPPORTED",
    4.18: "GAMEMANAGER_ERR_GAME_BUSY",
    4.19: "GAMEMANAGER_ERR_SESSION_TEMPLATE_NOT_SUPPORTED",
    "4.20": "GAMEMANAGER_ERR_GAME_MODE_ATTRIBUTE_MISSING",
    4.21: "GAMEMANAGER_ERR_FAILED_DUE_TO_FRIENDS_ONLY_RESTRICTION",
    4.22: "GAMEMANAGER_ERR_FAILED_DUE_TO_PRESENCE_MODE_RESTRICTION",
    4.23: "GAMEMANAGER_ERR_SPECTATOR_SLOTS_FULL",
    4.24: "GAMEMANAGER_ERR_UNRESPONSIVE_GAME_STATE",
    4.25: "GAMEMANAGER_ERR_GAME_DESTROYED_BY_CONNECTION_UPDATE",
    4.26: "GAMEMANAGER_ERR_INVALID_PING_SITE_ALIAS",
    4.27: "GAMEMANAGER_ERR_CROSSPLAY_DISABLED",
    4.28: "GAMEMANAGER_ERR_CROSSPLAY_DISABLED_USER",
    4.29: "GAMEMANAGER_ERR_UNEXPECTED_JOIN_FAILURE_GAME_VERSION",
    "4.30": "GAMEMANAGER_ERR_PERMISSION_DENIED",
    4.31: "GAMEMANAGER_ERR_ALREADY_ADMIN",
    4.32: "GAMEMANAGER_ERR_NOT_IN_ADMIN_LIST",
    4.33: "GAMEMANAGER_ERR_DEDICATED_SERVER_HOST",
    "4.50": "GAMEMANAGER_ERR_INVALID_QUEUE_METHOD",
    4.51: "GAMEMANAGER_ERR_PLAYER_NOT_IN_QUEUE",
    4.52: "GAMEMANAGER_ERR_DEQUEUE_WHILE_MIGRATING",
    4.53: "GAMEMANAGER_ERR_DEQUEUE_WHILE_IN_PROGRESS",
    4.101: "GAMEMANAGER_ERR_PLAYER_NOT_FOUND",
    4.103: "GAMEMANAGER_ERR_ALREADY_GAME_MEMBER",
    4.104: "GAMEMANAGER_ERR_REMOVE_PLAYER_FAILED",
    4.107: "GAMEMANAGER_ERR_INVALID_PLAYER_PASSEDIN",
    4.108: "GAMEMANAGER_ERR_JOIN_PLAYER_FAILED",
    4.109: "GAMEMANAGER_ERR_MISSING_PRIMARY_LOCAL_PLAYER",
    "4.110": "GAMEMANAGER_ERR_PLAYER_BANNED",
    4.111: "GAMEMANAGER_ERR_GAME_ENTRY_CRITERIA_FAILED",
    4.112: "GAMEMANAGER_ERR_ALREADY_IN_QUEUE",
    4.113: "GAMEMANAGER_ERR_ENFORCING_SINGLE_GROUP_JOINS",
    4.114: "GAMEMANAGER_ERR_BANNED_PLAYER_NOT_FOUND",
    4.115: "GAMEMANAGER_ERR_BANNED_LIST_MAX",
    4.117: "GAMEMANAGER_ERR_FAILED_REPUTATION_CHECK",
    "4.120": "GAMEMANAGER_ERR_RESERVATION_ALREADY_EXISTS",
    4.121: "GAMEMANAGER_ERR_NO_RESERVATION_FOUND",
    4.122: "GAMEMANAGER_ERR_INVALID_GAME_ENTRY_TYPE",
    4.151: "GAMEMANAGER_ERR_INVALID_GROUP_ID",
    4.152: "GAMEMANAGER_ERR_PLAYER_NOT_IN_GROUP",
    "4.200": "GAMEMANAGER_ERR_INVALID_MATCHMAKING_CRITERIA",
    4.201: "GAMEMANAGER_ERR_UNKNOWN_MATCHMAKING_SESSION_ID",
    4.202: "GAMEMANAGER_ERR_NOT_MATCHMAKING_SESSION_OWNER",
    4.203: "GAMEMANAGER_ERR_MATCHMAKING_NO_JOINABLE_GAMES",
    4.205: "GAMEMANAGER_ERR_MATCHMAKING_USERSESSION_NOT_FOUND",
    4.206: "GAMEMANAGER_ERR_MATCHMAKING_EXCEEDED_MAX_REQUESTS",
    4.207: "GAMEMANAGER_ERR_MATCHMAKING_USER_GROUP_EXCEEDED_MAX_REQUESTS",
    "4.230": "GAMEMANAGER_ERR_PLAYER_CAPACITY_TOO_SMALL",
    4.231: "GAMEMANAGER_ERR_PLAYER_CAPACITY_TOO_LARGE",
    4.232: "GAMEMANAGER_ERR_PLAYER_CAPACITY_IS_ZERO",
    4.233: "GAMEMANAGER_ERR_MAX_PLAYER_CAPACITY_TOO_LARGE",
    "4.250": "GAMEMANAGER_ERR_INVALID_TEAM_CAPACITIES_VECTOR_SIZE",
    4.251: "GAMEMANAGER_ERR_DUPLICATE_TEAM_CAPACITY",
    4.252: "GAMEMANAGER_ERR_INVALID_TEAM_ID_IN_TEAM_CAPACITIES_VECTOR",
    4.253: "GAMEMANAGER_ERR_TEAM_NOT_ALLOWED",
    4.254: "GAMEMANAGER_ERR_TOTAL_TEAM_CAPACITY_INVALID",
    4.255: "GAMEMANAGER_ERR_TEAM_FULL",
    4.257: "GAMEMANAGER_ERR_PLAYER_CAPACITY_NOT_EVENLY_DIVISIBLE_BY_TEAMS",
    "4.270": "GAMEMANAGER_ERR_EMPTY_ROLE_CAPACITIES",
    4.271: "GAMEMANAGER_ERR_ROLE_CAPACITY_TOO_SMALL",
    4.272: "GAMEMANAGER_ERR_ROLE_CAPACITY_TOO_LARGE",
    4.273: "GAMEMANAGER_ERR_ROLE_NOT_ALLOWED",
    4.274: "GAMEMANAGER_ERR_ROLE_FULL",
    4.275: "GAMEMANAGER_ERR_ROLE_CRITERIA_INVALID",
    4.276: "GAMEMANAGER_ERR_ROLE_CRITERIA_FAILED",
    4.277: "GAMEMANAGER_ERR_MULTI_ROLE_CRITERIA_INVALID",
    4.278: "GAMEMANAGER_ERR_MULTI_ROLE_CRITERIA_FAILED",
    4.301: "GAMEMANAGER_ERR_NO_DEDICATED_SERVER_FOUND",
    4.302: "GAMEMANAGER_ERR_DEDICATED_SERVER_ONLY_ACTION",
    4.303: "GAMEMANAGER_ERR_DEDICATED_SERVER_HOST_CANNOT_JOIN",
    4.304: "GAMEMANGER_ERR_MACHINE_ID_LIST_EMPTY",
    4.308: "GAMEMANAGER_ERR_NO_HOSTS_AVAILABLE_FOR_INJECTION",
    4.401: "GAMEBROWSER_ERR_INVALID_CRITERIA",
    4.402: "GAMEBROWSER_ERR_INVALID_CAPACITY",
    4.403: "GAMEBROWSER_ERR_INVALID_LIST_ID",
    4.404: "GAMEBROWSER_ERR_NOT_LIST_OWNER",
    4.405: "GAMEBROWSER_ERR_INVALID_LIST_CONFIG_NAME",
    4.406: "GAMEBROWSER_ERR_CANNOT_GET_USERSET",
    4.407: "GAMEBROWSER_ERR_EXCEED_MAX_SYNC_SIZE",
    4.408: "GAMEBROWSER_ERR_EXCEEDED_MAX_REQUESTS",
    4.409: "GAMEBROWSER_ERR_SEARCH_ERR_OVERLOADED",
    "4.410": "GAMEBROWSER_ERR_CROSS_PLATFORM_OPTOUT",
    4.411: "GAMEBROWSER_ERR_DISALLOWED_PLATFORM",
    4.502: "GAMEMANAGER_ERR_GAME_CAPACITY_TOO_SMALL",
    4.503: "GAMEMANAGER_ERR_INVALID_ACTION_FOR_GROUP",
    4.505: "GAMEMANAGER_ERR_MIGRATION_NOT_SUPPORTED",
    4.506: "GAMEMANAGER_ERR_INVALID_NEWHOST",
    4.508: "GAMEMANAGER_ERR_INVALID_PERSISTED_GAME_ID_OR_SECRET",
    4.509: "GAMEMANAGER_ERR_PERSISTED_GAME_ID_IN_USE",
    "4.510": "GAMEMANAGER_ERR_USER_NOT_FOUND",
    4.511: "GAMEMANAGER_ERR_USER_ALREADY_AUDITED",
    4.512: "GAMEMANAGER_ERR_USER_CURRENTLY_AUDITED",
    "4.600": "GAMEMANAGER_ERR_INVALID_SCENARIO_NAME",
    4.601: "GAMEMANAGER_ERR_INVALID_SCENARIO_ID",
    4.602: "GAMEMANAGER_ERR_NOT_SCENARIO_OWNER",
    4.603: "GAMEMANAGER_ERR_MISSING_SCENARIO_ATTRIBUTE",
    4.604: "GAMEMANAGER_ERR_INVALID_SCENARIO_ATTRIBUTE",
    4.605: "GAMEMANAGER_ERR_INVALID_SCENARIO_VARIANT",
    "4.610": "GAMEMANAGER_ERR_INVALID_TEMPLATE_NAME",
    4.611: "GAMEMANAGER_ERR_MISSING_TEMPLATE_ATTRIBUTE",
    4.612: "GAMEMANAGER_ERR_INVALID_TEMPLATE_ATTRIBUTE",
    "4.620": "GAMEMANAGER_ERR_INPUT_SANITIZER_FAILURE",
    "4.700": "GAMEMANAGER_ERR_EXTERNAL_SERVICE_BUSY",
    4.701: "GAMEMANAGER_ERR_EXTERNAL_SESSION_IMAGE_INVALID",
    4.702: "GAMEMANAGER_ERR_EXTERNAL_SESSION_CUSTOM_DATA_TOO_LARGE",
    4.703: "GAMEMANAGER_ERR_EXTERNAL_SESSION_ERROR",
    4.704: "GAMEMANAGER_ERR_EXTERNALSESSION_INVALID_SANDBOX_ID",
    5.1: "REDIRECTOR_SERVER_NOT_FOUND",
    5.2: "REDIRECTOR_NO_SERVER_CAPACITY",
    5.3: "REDIRECTOR_NO_MATCHING_INSTANCE",
    5.4: "REDIRECTOR_SERVER_NAME_ALREADY_IN_USE",
    5.5: "REDIRECTOR_CLIENT_NOT_COMPATIBLE",
    5.6: "REDIRECTOR_CLIENT_UNKNOWN",
    5.7: "REDIRECTOR_UNKNOWN_CONNECTION_PROFILE",
    5.8: "REDIRECTOR_SERVER_SUNSET",
    5.9: "REDIRECTOR_SERVER_DOWN",
    "5.10": "REDIRECTOR_INVALID_PARAMETER",
    5.11: "REDIRECTOR_UNKNOWN_SERVICE_NAME",
    5.12: "REDIRECTOR_PAST_EVENT",
    5.13: "REDIRECTOR_UNKNOWN_SCHEDULE_ID",
    5.14: "REDIRECTOR_MISSING_SERVICE_NAME",
    7.1: "STATS_ERR_CONFIG_NOTAVAILABLE",
    7.2: "STATS_ERR_INVALID_LEADERBOARD_ID",
    7.3: "STATS_ERR_INVALID_FOLDER_ID",
    7.4: "STATS_ERR_UNKNOWN_CATEGORY",
    7.5: "STATS_ERR_STAT_NOT_FOUND",
    7.6: "STATS_ERR_BAD_PERIOD_TYPE",
    7.7: "STATS_ERR_NO_DB_CONNECTION",
    7.8: "STATS_ERR_DB_DATA_NOT_AVAILABLE",
    7.9: "STATS_ERR_UNKNOWN_STAT_GROUP",
    "7.10": "STATS_ERR_DB_TRANSACTION_ERROR",
    7.11: "STATS_ERR_INVALID_UPDATE_TYPE",
    7.13: "STATS_ERR_DB_QUERY_FAILED",
    7.14: "STATS_ERR_RANK_OUT_OF_RANGE",
    7.15: "STATS_ERR_BAD_PERIOD_OFFSET",
    7.16: "STATS_ERR_BAD_SCOPE_INFO",
    7.17: "STATS_ERR_INVALID_FOLDER_NAME",
    7.18: "STATS_ERR_OPERATION_IN_PROGRESS",
    "7.20": "STATS_ERR_INVALID_OPERATION",
    7.21: "STATS_ERR_INVALID_OBJECT_ID",
    7.22: "STATS_ERR_BAD_PERIOD_COUNTER",
    7.23: "STATS_ERR_LEADERBOARD_NOT_IN_MEMORY",
    "9.100": "UTIL_CONFIG_NOT_FOUND",
    9.101: "UTIL_PSU_LIMIT_EXCEEDED",
    9.102: "UTIL_SERVICENAME_NOT_SPECIFIED",
    9.103: "UTIL_SERVICENAME_NOT_HOSTED",
    9.104: "UTIL_CALLER_PLATFORM_NOT_FOUND",
    9.105: "UTIL_CALLER_PLATFORM_MISMATCH",
    9.106: "UTIL_CALLER_PLATFORM_NOT_ALLOWED",
    "9.150": "UTIL_TELEMETRY_NO_SERVERS_AVAILABLE",
    9.151: "UTIL_TELEMETRY_OUT_OF_MEMORY",
    9.152: "UTIL_TELEMETRY_KEY_TOO_LONG",
    9.155: "UTIL_TICKER_NO_SERVERS_AVAILABLE",
    9.156: "UTIL_TICKER_KEY_TOO_LONG",
    "9.200": "UTIL_USS_RECORD_NOT_FOUND",
    9.201: "UTIL_USS_TOO_MANY_KEYS",
    9.202: "UTIL_USS_DB_ERROR",
    "9.250": "UTIL_USS_USER_NO_EXTENDED_DATA",
    "9.300": "UTIL_SUSPEND_PING_TIME_TOO_LARGE",
    9.301: "UTIL_SUSPEND_PING_TIME_TOO_SMALL",
    15.1: "MESSAGING_ERR_UNKNOWN",
    15.2: "MESSAGING_ERR_MAX_ATTR_EXCEEDED",
    15.3: "MESSAGING_ERR_DATABASE",
    15.4: "MESSAGING_ERR_TARGET_NOT_FOUND",
    15.5: "MESSAGING_ERR_TARGET_TYPE_INVALID",
    15.6: "MESSAGING_ERR_TARGET_INBOX_FULL",
    15.7: "MESSAGING_ERR_MATCH_NOT_FOUND",
    15.8: "MESSAGING_ERR_FEATURE_DISABLED",
    15.9: "MESSAGING_ERR_INVALID_PARAM",
    "15.10": "MESSAGING_ERR_PROFANITY_SYSTEM",
    25.1: "ASSOCIATIONLIST_ERR_USER_NOT_FOUND",
    25.2: "ASSOCIATIONLIST_ERR_DUPLICATE_USER_FOUND",
    25.3: "ASSOCIATIONLIST_ERR_CANNOT_INCLUDE_SELF",
    25.4: "ASSOCIATIONLIST_ERR_INVALID_USER",
    25.5: "ASSOCIATIONLIST_ERR_MEMBER_ALREADY_IN_THE_LIST",
    25.6: "ASSOCIATIONLIST_ERR_MEMBER_NOT_FOUND_IN_THE_LIST",
    "25.10": "ASSOCIATIONLIST_ERR_LIST_NOT_FOUND",
    25.11: "ASSOCIATIONLIST_ERR_LIST_IS_FULL_OR_TOO_MANY_USERS",
    25.16: "ASSOCIATIONLIST_ERR_PAIRED_LIST_MODIFICATION_NOT_SUPPORTED",
    25.17: "ASSOCIATIONLIST_ERR_PAIRED_LIST_IS_FULL_OR_TOO_MANY_USERS",
    25.18: "ASSOCIATIONLIST_ERR_SUBSCRIBE_USER_LIST_NOT_SUPPORTED",
    28.1: "GAMEREPORTING_ERR_UNEXPECTED_REPORT",
    "28.100": "GAMEREPORTING_COLLATION_ERR_NO_VALID_REPORTS",
    28.101: "GAMEREPORTING_COLLATION_ERR_NO_REPORTS",
    28.102: "GAMEREPORTING_COLLATION_REPORTS_INCONSISTENT",
    28.103: "GAMEREPORTING_COLLATION_ERR_MISSING_GAME_ATTRIBUTE",
    28.104: "GAMEREPORTING_COLLATION_ERR_INVALID_GAME_ATTRIBUTE",
    "28.200": "GAMEREPORTING_CUSTOM_ERR_PROCESSING_FAILED",
    28.201: "GAMEREPORTING_CONFIG_ERR_MISSING_PROCESSOR_ATTRIBUTE",
    28.202: "GAMEREPORTING_CONFIG_ERR_INVALID_PROCESSOR_ATTRIBUTE",
    28.203: "GAMEREPORTING_CONFIG_ERR_STAT_UPDATE_FAILED",
    28.204: "GAMEREPORTING_CUSTOM_ERR_PROCESS_UPDATED_STATS_FAILED",
    28.205: "GAMEREPORTING_ERR_INVALID_GAME_TYPE",
    28.301: "GAMEREPORTING_OFFLINE_ERR_INVALID_GAME_TYPE",
    28.302: "GAMEREPORTING_OFFLINE_ERR_REPORT_INVALID",
    28.401: "GAMEREPORTING_TRUSTED_ERR_INVALID_GAME_TYPE",
    28.402: "GAMEREPORTING_TRUSTED_ERR_REPORT_INVALID",
    28.501: "GAMEHISTORY_ERR_UNKNOWN_QUERY",
    28.502: "GAMEHISTORY_ERR_INVALID_COLUMNKEY",
    28.503: "GAMEHISTORY_ERR_INVALID_FILTER",
    28.504: "GAMEHISTORY_ERR_INVALID_GAMETYPE",
    28.505: "GAMEHISTORY_ERR_UNKNOWN_VIEW",
    28.506: "GAMEHISTORY_ERR_INVALID_QUERY",
    28.507: "GAMEHISTORY_ERR_MISSING_QVARS",
    28.508: "GAMEHISTORY_ERR_INVALID_QVARS",
    1031.504: "FRIENDS_UNKNOWN_ERR",
    1031.901: "FRIENDS_NO_SUCH_USER",
    1031.909: "FRIENDS_INVALID_ARGUMENT_TYPE",
    "1031.11100": "FRIENDS_API_KEY_INVALID",
    1031.20006: "FRIENDS_NO_ID_SPECIFIED",
    1031.20007: "FRIENDS_NO_FRIEND_SPECIFIED",
    1031.20008: "FRIENDS_BAD_PRIVACY_SETTING",
    1031.22001: "FRIENDS_FRIENDS_LIST_FULL",
    1031.22002: "FRIENDS_NO_GROUP_NAME_SPECIFIED",
    1031.22003: "FRIENDS_GLOBAL_GROUP_LOCKED",
    1031.22004: "FRIENDS_AUTHTOKEN_INVALID",
    1031.22005: "FRIENDS_AUTHTOKEN_INVALID_FOR_USER",
    1031.22006: "FRIENDS_INVITATION_NOT_FOUND",
    1031.22007: "FRIENDS_TARGET_NOT_FRIEND",
    1031.22008: "FRIENDS_TARGET_IN_CUSTOM_GROUP",
    1031.22009: "FRIENDS_INVITEE_BLOCKED_BY_INVITER",
    "1031.22010": "FRIENDS_INVITEE_ALREADY_FRIEND",
    1031.22013: "FRIENDS_FRIEND_NOT_IN_GROUP",
    1031.22015: "FRIENDS_BLOCKING_SELF",
    1031.22016: "FRIENDS_BATCH_INVITE_ERROR",
    1031.22017: "FRIENDS_INVITEE_IS_INVITER",
    1031.22018: "FRIENDS_ID_LIST_EMPTY",
    "1031.22020": "FRIENDS_OUTBOUND_INVITES_FULL",
    1031.22021: "FRIENDS_INBOUND_INVITES_FULL",
    1031.22022: "FRIENDS_FAVORITING_SELF",
    1031.22023: "FRIENDS_UNFAVORITING_SELF",
    1031.22024: "FRIENDS_FAVORITING_NON_FRIEND",
    1031.22025: "FRIENDS_UNFAVORITING_NON_FRIEND",
    1031.22026: "FRIENDS_NAMESPACE_MISTMATCH",
    30722.1: "USER_ERR_USER_NOT_FOUND",
    30722.2: "USER_ERR_SESSION_NOT_FOUND",
    30722.3: "USER_ERR_DUPLICATE_SESSION",
    30722.4: "USER_ERR_NO_EXTENDED_DATA",
    30722.5: "USER_ERR_MAX_DATA_REACHED",
    30722.6: "USER_ERR_KEY_NOT_FOUND",
    30722.7: "USER_ERR_INVALID_SESSION_INSTANCE",
    30722.8: "USER_ERR_INVALID_PARAM",
    30722.9: "USER_ERR_MINIMUM_CHARACTERS",
    "30722.10": "ACCESS_GROUP_ERR_INVALID_GROUP",
    30722.11: "ACCESS_GROUP_ERR_DEFAULT_GROUP",
    30722.12: "ACCESS_GROUP_ERR_NOT_CURRENT_GROUP",
    30722.13: "ACCESS_GROUP_ERR_CURRENT_GROUP",
    30722.14: "ACCESS_GROUP_ERR_NO_GROUP_FOUND",
    30722.15: "GEOIP_INCOMPLETE_PARAMETERS",
    30722.16: "USER_ERR_GEOIP_LOOKUP_FAILED",
    30722.17: "ERR_ENTITY_TYPE_NOT_FOUND",
    30722.18: "ERR_ENTITY_NOT_FOUND",
    30722.19: "ERR_NOT_SUPPORTED",
    "30722.20": "USER_ERR_EXISTS",
    30722.23: "GEOIP_ERR_USER_OPTOUT",
    "30722.30": "USER_ERR_CROSS_PLATFORM_OPTOUT",
    30722.31: "USER_ERR_DISALLOWED_PLATFORM",
}

ErrorMessage = {
    0.1: "General system error.",
    0.2: "Component not found.",
    0.3: "Command not found.",
    0.4: "This command requires you to log in/provide access credentials.",
    0.5: "Command timed out.",
    0.6: "Calling session got disconnected.",
    0.7: "Duplicate user has logged in.",
    0.8: "You don't have the required permission to perform the action.",
    0.9: "The job, fiber or action has been canceled.",
    "0.10": "General error thrown by a RPC's custom request hook, triggered by custom code.",
    0.11: "General error thrown by a RPC's custom response hook, triggered by custom code.",
    0.12: "General error thrown by TDF decoder when incoming string exceeds max length defined for this field in TDF",
    0.13: "General error thrown by TDF decoder when incoming enum value is not a defined enum for this field in TDF",
    0.14: "The command must be executed on a Blaze instance that owns the object refered to by the request.",
    0.16: "The command fibers have hit the max limit.",
    0.17: "The command has come on the wrong endpoint.",
    0.18: "General error thrown by TDF encoder on client when string is not valid utf8.",
    0.19: "The service is currently unavailable. This is most likely a transient condition and may be corrected by retrying with a backoff.",
    "0.20": "Action only allowed for a primary persona.",
    0.101: "General DB error",
    0.102: "Not connected to the DB",
    0.103: "Operation not supported",
    0.104: "No connection could be obtained",
    0.105: "A duplicate entry already exists in the DB",
    0.106: "Table does not exist",
    0.107: "Lost connection to DB",
    0.108: "Request timed out",
    0.109: "Driver initialization failed",
    "0.110": "Connection was released while a transaction was pending",
    0.111: "Deadlock",
    0.112: "Non existent partition",
    0.113: "Partition already exists",
    0.114: "**Deprecated - use ERR_TIMEOUT instead** The server is too busy to process this request.  Please try again later.",
    0.115: "This error will be returned when a guest session tries to call a RPC which is not allowed guest session to call.",
    0.116: "The user defined error.",
    0.117: "Could not connect to external service when making request. Equivalent to curl error (7).",
    0.118: "A transaction is already in progress",
    0.119: "No rows were affected as a result of the query.",
    "0.120": "No such db thread",
    1.1: "The token provided was not a valid Identity 2.0 access token",
    1.2: "Generic error if specific error could not be obtained for a failed request.",
    1.3: "The auth code provided could not be used to log to requested service.",
    1.4: "The platform that service belongs to is not allowed in the login command.",
    1.6: "The supplied sandbox id is not supported on this server.",
    "1.10": "The country does not exist or is invalid",
    1.11: "The user does not exist or is invalid",
    1.12: "The supplied password was invalid",
    1.14: "The supplied auth token was expired",
    1.15: "Cannot create user/persona because email/displayname already exists",
    1.16: "User is too young, cannot create underage account",
    1.17: "The account does not exist",
    1.18: "The requested persona name was not found.",
    1.19: "The requested persona is not active.",
    "1.20": "Invalid parental email.",
    1.21: "One of the input fields was invalid",
    1.22: "Invalid email",
    1.23: "Invalid account status",
    1.32: "The requested persona is banned.",
    1.33: "The persona does not exist or is invalid",
    1.34: "The command required the current password of the Nucleus account, the password provided is incorrect.",
    1.41: "Account deactivated",
    1.43: "Account banned from online play",
    1.44: "Account has been disabled",
    1.45: "Account is already logged in with a different persona.",
    1.46: "There was a restriction violation on a product",
    1.47: "More than one wallet was found.",
    1.48: "The product is not configured correctly in the catalog",
    1.49: "The product in the catalog does not have an entitlement tag defined",
    "1.50": "Received an invalid response from the upstream server",
    1.54: "No Associated product for this key",
    1.55: "Invalid Mapping",
    1.56: "Group name not found",
    1.57: "Missing personaId",
    1.58: "User not match with persona",
    1.59: "Group name failed whilelist test",
    "1.60": "Failed linking persona and entitlement",
    1.61: "No such group",
    1.63: "No active entitlement matching the criteria",
    1.66: "The use count for the user's entitlement is 0",
    1.67: "The entitlement tag is required but it's empty string in requirement",
    "1.70": "Groupname is required",
    1.71: "A groupname in group list is empty string",
    1.72: "Too many entitlements were found in Nucleus. Requestor should use pagination to fetch smaller result sets.",
    1.73: "Page size in the request can't be 0",
    1.74: "Entitlement tag is required",
    1.75: "Page No in the request can't be 0",
    1.76: "You only can modify an entitlement status to DISABLED, DELETED or BANNED",
    1.77: "You only can decrement the use count and can not increment it",
    1.78: "You can't make the termination date LATER, than it is currently.  You can make it sooner",
    1.79: "The entitlement doesn't exist",
    "1.80": "PSU cutoff for entitlement type is less than the current connected user count",
    1.81: "The opt-in name is required",
    1.82: "The provided opt-in is invalid ",
    1.83: "German double opt in cannot be set to true if global opt in is not set to true as well",
    1.84: "The user has not opted in for the given optInType",
    1.85: "The ID of the external authentication data is required",
    1.86: "The ID for the persona external reference is required",
    1.87: "The requestor/game that is adding the external authentication is required",
    1.88: "The application ID or other identifier associated with the external authentication token is required",
    1.89: "The token associated with the external authentication is required",
    "1.90": "The input parameter exceeds the max value (255) on the server",
    1.91: "The persona external reference does not exist",
    1.93: "Invalid source provided. The source doesn't exist in the system",
    1.94: "The authentication data does not exist",
    1.95: "Account already has primary persona and updating primary via authentication is not allowed at this time.",
    1.101: "User's status is inactive (disabled, banned, etc.)",
    1.102: "User isn't awaiting activation.",
    1.103: "User's display name doesn't match gamer tag.",
    1.105: "The supplied namespace was invalid.",
    1.198: "The value contains invalid characters.",
    1.199: "The value is too short.",
    "1.200": "The value is too long.",
    1.201: "The value contains invalid characters.",
    1.202: "The value is too short.",
    1.204: "The value must begin with a letter.",
    1.205: "The value is missing.",
    1.206: "The value is not valid for this field.",
    1.207: "The value contains profanity or disallowed text.",
    1.208: "The value must contain special characters.",
    1.209: "The value already exists.",
    "1.210": "The user requires parental consent.",
    1.211: "The user is too young to create an account.",
    1.212: "The associated user is too young, cannot associate with the new created account.",
    "1.300": "The maximum number of personas in this namespace has been reached for this account. Cannot create additional personas.",
    1.303: "The user requires parental mail.",
    1.307: "A parent session is required to create a guest MLU session but was not found.",
    1.308: "No XBL token is available for the user. This is likely due to the user being externally available in MS, but not 'known' to Nucleus.",
    1.309: "No PSN token is available for the user. This is likely due to the user being externally available in PSN, but not 'known' to Nucleus.",
    "1.310": "Attempting to grant an entitlement outside the trial period. For period trials, this means the current time is outside the start/end time. For managed lifecycle, this means we attempt to grant an entitlement with a status that doesn't match based on the start/end time (PENDING, ACTIVE, DISABLED for before, during, and after, respectively.)",
    1.311: "The first party token expired for the user. The user may need to relogin to Nucleus with a new token.",
    4.1: "A generic problem with the combination of game settings you're trying to use.  For example, you might be trying to change a game's ranked setting on the 360 after game creation (not allowed by XBL).",
    4.2: "The blazeServer wasn't able to find a game with the supplied gameId.",
    4.3: "The player's game join failed because the game doesn't allow players to join using the supplied JoinMethod.",
    4.4: "The game doesn't have enough available participant slots. Note: If you need a public slot and none are available, you'll get PARTICIPANT_SLOTS_FULL, even if there are open private slots.",
    4.5: "The game refused to change state because it would violate the GameState state machine's allowed transitions.",
    4.6: "This RPC cannot be issued while the game is in its current state (GameState).",
    4.7: "System error: a game couldn't be destroyed for an unknown reason.",
    4.8: "The game is full, and there's not enough room in the game's queue for you (or your entire group would overflow the queue).",
    4.9: "The game failed to parse the game entry criteria.",
    "4.10": "There was a mismatch in game protocol versions",
    4.11: "The game has already started, and does not allow join in progress.",
    4.12: "DEPRECATED - Reserved ids are only allowed if dynamically created dedicated server mode is enabled and the ID isn't already taken by an existing game.",
    4.13: "The join method specified is not valid for the type of join attempted. System join methods not allowed, as well as specifying JOIN_BY_PLAYER with no player information(joinByGameId)",
    4.14: "The requested slot was occupied- should only occur when attempting to join a specific slot in a game.",
    4.15: "The requested action can only be performed on a virtualized game.",
    4.16: "The player requesting this action must be the topology host of the game.",
    4.17: "The requested network topology is not supported by the game type.",
    4.18: "The join cannot be completed while the game is in a busy GameState or locked as busy. Callers may try again later.",
    4.19: "The requested external session template name is not supported by the server.",
    "4.20": "A game create or reset attempt didn't specify the game mode attribute.",
    4.21: "The game's join restrictions prevented the user from joining.",
    4.22: "The game's presence mode prevented the user from joining.",
    4.23: "The game doesn't have enough available spectator slots. Note: If you need a public slot and none are available, you'll get SPECTATOR_SLOTS_FULL, even if there are open private slots.",
    4.24: "This RPC cannot be issued while the game is in its current state (GameState == UNRESPONSIVE).",
    4.25: "The game was destroyed by the connection update. This is not an unexpected error, but must be handled differently from a ERR_OK. (Pointers to the Game may be invalidated.)",
    4.26: "The ping site alias provided for the dedicated server game was invalid or unknown to QoS.",
    4.27: "The crossplay settings of the Game or Request prevent the action from occurring.",
    4.28: "The crossplay settings of the User prevent the action from occurring.  Can be fixed by changing the Crossplay Enabled setting of the User.",
    4.29: "Unexpected game record version detected while trying to join the game.",
    "4.30": "You must have admin rights on the game to issue this RPC.",
    4.31: "A player to be added to admin list is already an admin.",
    4.32: "A player to be removed from admin list is actually not an admin.",
    4.33: "A player to be removed from admin list is host of dedicated server, can't be removed.",
    "4.50": "The server configured queueing method does not allow this call to be made.",
    4.51: "The player that is being added to the game from the queue, is not currently in the queue.",
    4.52: "The server will not process a queued player while the game is migrating.  This is because the player will then fail to establish network connecitons.",
    4.53: "The server will not process a queued player while the game is in progress and in progress joins are not allowed.",
    4.101: "The player wasn't found in a game, or the player may not exist at all (Note: we don't hit the user DB to validate a user's existence).",
    4.103: "The player can't join the game; he's already a game member",
    4.104: "System Error: a player was banned from the game, but couldn't be removed.",
    4.107: "Attempting to take an action on an existing (but invalid) player.  For example, banning or kicking yourself out of a game.",
    4.108: "A Dedicated server reset attempt failed because we couldn't join the game after resetting it.",
    4.109: "The primry local player is not fully in game, so the other local users cannot join.",
    "4.110": "The player's joinGame failed; he's been banned from this game.",
    4.111: "The player's joinGame failed; he doesn't satisfy the game's EntryCriteria formula.  See the JoinGameCb's entry criteria string for details.",
    4.112: "The player can't join the game; they are already in the queue.",
    4.113: "The player can't join the game; the game's GameSettings::setEnforceSingleGroupJoin flag is set and the game already has 2 game groups in it.",
    4.114: "The player isn't found in the banned list of the game, remove the banned player failure.",
    4.115: "The banned players have exceed the max banned users numable that setting at configuration file.",
    4.117: "The player failed to pass the reputation check on a game that doesn't allow any reputation value.",
    "4.120": "The player cannot make a reservation for a game they already have a reservation in.",
    4.121: "The player can only claim reservations in a game in which they have made a reservation.",
    4.122: "The type of entry requested is not valid for the join method.  Matchmaking does not allow claiming reservations.",
    4.151: "The blazeServer wasn't able to find a user group with the supplied id.",
    4.152: "The player is not a member of the specified group.",
    "4.200": "Problem parsing your matchmaking criteria.  See StartMatchmakingError's err msg for details.",
    4.201: "No matchmaking sessions exist with the id you specified.",
    4.202: "You can only cancel matchmaking sessions that you are the owner of.",
    4.203: "None of the games provided from find game matchmaking were joinable.",
    4.205: "The owning user session for the session was not found.",
    4.206: "The number of concurrent active matchmaking sessions exceeded configured maximum.",
    4.207: "The number of concurrent active user group matchmaking sessions exceeded configured maximum.",
    "4.230": "The capacity changes are invalid, it's less than current players count of the game, or the minimum capacity.",
    4.231: "The capacity changes are invalid, it's above the maximum players capacity of the game.",
    4.232: "The capacity changes are invalid, the total participant count can't be 0.",
    4.233: "The blazeServer has a compile-time limit of SLOT_ID_MAX_COUNT players in a game (256 by default).  See gamesessionmaster.h on the server.",
    "4.250": "A game must have 1 or more teams specified.",
    4.251: "vector contains multiple nodes for a single teamId.",
    4.252: "INVALID_TEAM_ID is reserved, and cannot be used in the team capacities vector.",
    4.253: "trying to perform a team operation on a game or player for a team that isn't allowed (not present in capacity vector)",
    4.254: "sum of all teams is not equal to the game's total capacity.",
    4.255: "trying to join a full team, or a team that you cannot fit your entire game group into.",
    4.257: "A game's total player capacity must be evenly divisible amongst the teams present in the game.",
    "4.270": "Role capacities were empty.",
    4.271: "Sum of role capacities were too small to complete a team.",
    4.272: "A role's capacity was specified as larger than the game's team capacity.",
    4.273: "Operation on a role not present in the game.",
    4.274: "The selected role is full.",
    4.275: "The game failed to parse the role criteria.",
    4.276: "The player's joinGame failed; he doesn't satisfy the game's RoleCriteria formula.  See the JoinGameCb's entry criteria string for details.",
    4.277: "The game failed to parse the multirole criteria.",
    4.278: "The player's joinGame failed; he doesn't satisfy the game's MultiRoleCriteria formula.",
    4.301: "No dedicated game servers were found (none are available to reset).",
    4.302: "An invalid action was attempted, this action is only available for dedicated server network topology",
    4.303: "Host can't join his own dedicated server game.",
    4.304: "A dynamic dedicated server creator attempted to register itself with no machine ids provided",
    4.308: "Virtual game materialization failed because suitable dedicated server for game injection wasn't found.",
    4.401: "Problem parsing your game browsing criteria.  See GetGameListError's err msg for details.",
    4.402: "A list's capacity must be GAME_LIST_CAPACITY_UNLIMITED, or greater than zero.",
    4.403: "No list subscription found on the blazeServer with the supplied id.",
    4.404: "This operation can only be completed by the list owner.",
    4.405: "The gameBrowser can't find a list configuration with the supplied listConfigName.  (list configurations are defined in the server's gamebrowser config file).",
    4.406: "Cannot get user set from userset provider",
    4.407: "Cannot get games exceed max size set based on configs maxGameListSyncSize",
    4.408: "The number of concurrent active game browser lists exceeded configured maximum.",
    4.409: "The search slave is overloaded.",
    "4.410": "Action not allowed for user opted-out of cross platform.",
    4.411: "The action requested required a disallowed cross platform interaction.",
    4.502: "In valid game capacity settings, the desired capacity of the game to be created is less than the number of people that need to join the game.",
    4.503: "An invalid action was attempted, this action is only available for individual player instead of a group.",
    4.505: "An attempt to initiate host migration has occured on a game that does not support host migration.",
    4.506: "Suggested new host is a invalid candidate for host migration.",
    4.508: "The secret does not match with the guid.",
    4.509: "The persisted game id is in use.",
    "4.600": "The scenario name provided does not exist.",
    4.601: "The scenario id provided is invalid, or no longer exists.",
    4.602: "Attempting to perform an action on a scenario not owned by the current user.",
    4.603: "The Scenario being run requires an attribute that was not provided.",
    4.604: "A Scenario Attribute being used is currently invalid. (Generally indicates a config setup error.)",
    4.605: "The Scenario variant being used is currently invalid.",
    "4.610": "The RPC template name provided does not exist.",
    4.611: "The template being run requires an attribute that was not provided.",
    4.612: "A template Attribute being used is currently invalid. (Generally indicates a config setup error.)",
    "4.620": "The Input Sanitizer failed due to incorrect formatting or criteria failure.",
    "4.700": "The number of calls by the user for has exceeded the command's rate limit.",
    4.701: "The image for the external session is invalid or missing.",
    4.702: "The custom data for the external session is too large.",
    4.703: "General error returned when the external session service returns an unhandled error.",
    4.704: "The external session has invalid sandbox id.",
    5.1: "No Blaze cluster found matching the requested service name (getServerInstance/getServerInstanceHttp calls only)",
    5.2: "All endpoints matching the requested service name and connection profile have reached their connection limit (or are draining)",
    5.3: "No endpoints found matching the requested service name and connection profile",
    5.4: "(updateServerInfo/publishServerInstanceInfo calls only) A Blaze instance with the same instance id is already registered (under a different registration id) for the specified service name",
    5.5: "The client is incompatible with the Blaze cluster matching the requested service name (the client version is in the cluster's configured list of incompatible client versions)",
    5.6: "The client version is not recognized by the Blaze cluster matching the requested service name (the client version is not in the cluster's configured lists of compatible or incompatible client versions, and the cluster's list of compatible client versions is non-empty)",
    5.7: "The requested connection profile is not in the redirector's configured list of profiles",
    5.8: "The requested service name has been sunset",
    5.9: "The REDIRECTOR_SERVER_DOWN error code is no longer in use",
    "5.10": "The REDIRECTOR_INVALID_PARAMETER error code is no longer in use",
    5.11: "No Blaze cluster found matching the requested service name (scheduleServerDowntime calls only)",
    5.12: "(scheduleServerDowntime calls only) The downtime being scheduled has already begun",
    5.13: "(cancelServerDowntime calls only) No scheduled downtime found matching the requested id",
    5.14: "(findCACertificates calls only) Request specified a DirtyCert version without providing a service name",
    7.1: "Config data is not loaded",
    7.2: "Invalid leaderboard ID",
    7.3: "Invalid leaderboard folder ID",
    7.4: "Unknown category was requested",
    7.5: "Stat not found",
    7.6: "Bad period type",
    7.7: "Failed to obtain DB connection",
    7.8: "No data returned by DB",
    7.9: "Unknown stat group",
    "7.10": "Unsuccessful DB transaction",
    7.11: "Invalid update type",
    7.13: "DB query select failed",
    7.14: "Rank out of range",
    7.15: "Bad period offset",
    7.16: "Scope defination is not right.",
    7.17: "Requested folder is not found.",
    7.18: "Requested operation is already in progress.",
    "7.20": "Requested operation is not defined",
    7.21: "Invalid object ID is supplied",
    7.22: "Invalid period counter is supplied",
    7.23: "Leaderboard is not in the memory",
    "9.100": "config section not found",
    9.101: "PSU limit reached",
    9.102: "Caller did not specify a service name. Required for shared platforms cluster.",
    9.103: "Caller specified service name is not hosted on this instance.",
    9.104: "caller's platform is not available.",
    9.105: "caller is attempting to access a resource for a different platform. Explicit error mostly to assist debugging.",
    9.106: "caller is attempting to access a service it should not. Explicit error mostly to assist debugging.",
    "9.150": "There are no telemetry servers available",
    9.151: "Server is out of memory.",
    9.152: "Telemetry key is longer then it should be.",
    9.155: "There are no ticker servers available.",
    9.156: "Ticker key is too long to create.",
    "9.200": "Record not found in user small storage.",
    9.201: "Exceeded number of keys allowed for user.",
    9.202: "Database error while reading/writing.",
    "9.250": "There is no extended data for the given session.",
    "9.300": "Requested Ping Suspension time exceeds maximum configured time.",
    9.301: "Requested Ping Suspension time is less than the minimum time allowed for a connection (has to be greater than inactivityTimeout on the BlazeServer).",
    15.1: "Unknown error occurred.",
    15.2: "Exceeded maximum number of message attributes.",
    15.3: "Messaging database error.",
    15.4: "Message target not found.",
    15.5: "Message target type is invalid.",
    15.6: "Target message inbox is full.",
    15.7: "Match not found.",
    15.8: "Feature is disabled on the messaging server.",
    15.9: "Invalid parameter(s).",
    "15.10": "Message was tagged for profanity filtering but the profanity service returned an error. Message will not be delivered.",
    25.1: "The referenced user was not found.",
    25.2: "Duplicate ListMemberIds were found from the UpdateListMembersRequest.",
    25.3: "User cannot include themselves in a UpdateListMembersRequest.",
    25.4: "A ListMemberId in the request is invalid. The blaze id, external id, and persona are all invalid.",
    25.5: "Some members in the UpdateListMembersRequest are already in the association list.",
    25.6: "Some members in the UpdateListMembersRequest were not found in the association list.",
    "25.10": "Specified list does not exist or no list is specified.",
    25.11: "List is already full, can't add other members, or members in the list to be added are too many for the list max size. Used when FIFO for list is false",
    25.16: "User can not modify the Paired list(add/remove members) directly.",
    25.17: "Paired list is already full.",
    25.18: "Requestor can not subscribe/unsubscribe another user's list.",
    28.501: "Game report query requested is not defined.",
    28.502: "Game report query requested is not defined.",
    28.503: "Game report query requested is not defined.",
    28.504: "Game report query requested is not defined.",
    28.505: "Game report view requested is not defined.",
    28.506: "Game report query requested is invalid.",
    28.507: "Query variables are needed but not provided.",
    28.508: "Query variables provided are invalid.",
    1031.504: "An unknown error occurred within the server",
    1031.901: "Unable to locate a user with the specified ID",
    1031.909: "Invalid type",
    "1031.11100": "Requires API Key or Client Certificate",
    1031.20006: "Neither a Nucleus ID nor a Persona ID were passed to a method that expected one",
    1031.20007: "No friend ID was specified",
    1031.20008: "An invalid privacy setting was specified",
    1031.22001: "The inviter or invitee's friends list is at or above the limit",
    1031.22002: "No group name was specified when trying to remove a user from a group",
    1031.22003: "Can not remove a user from the global group",
    1031.22004: "The AuthToken in the request is invalid",
    1031.22005: "The AuthToken does not match the Nucleus ID",
    1031.22006: "The specified invitation was not found",
    1031.22007: "The target user is not a friend",
    1031.22008: "The target user is in a non-default group and must be removed",
    1031.22009: "The invitee is in the inviter's block list",
    "1031.22010": "The invitee is already in the inviter's friend list",
    1031.22013: "The friend isn't in the specified group",
    1031.22015: "The user is trying to block their own ID",
    1031.22016: "An error was encountered while doing a batch invite operation",
    1031.22017: "The user is trying to invite their own ID",
    1031.22018: "The specified ID list is empty",
    "1031.22020": "The user's outbound invite list is full",
    1031.22021: "The user's inbound invite list is full",
    1031.22022: "You can't favorite yourself",
    1031.22023: "You can't unfavorite yourself",
    1031.22024: "You can't favorite a user who is not a friend",
    1031.22025: "You can't unfavorite a user who is not a friend",
    1031.22026: "The namespaces don't match for the Persona IDs",
    30722.1: "The referenced user was not found.",
    30722.2: "The referenced session was not found.",
    30722.3: "The session could not be added because one from that user already exists.",
    30722.4: "The extended data could not be returned because there is no extended data for the given session",
    30722.5: "The extended data attribute could not be added because the maximum number of attributes has been reached for this session",
    30722.6: "The extended data attribute could not be removed because key was not found",
    30722.7: "The session did not belong to the calling instance.",
    30722.8: "Invalid parameter(s).",
    30722.9: "The minimum of characters is 3.",
    "30722.10": "The specified access group was not found.",
    30722.11: "The specified access group is default group for the referenced user.",
    30722.12: "The referenced user does not belong to the specified group.",
    30722.13: "The referenced user already belong to the specified group.",
    30722.14: "There is no group is found for specified external id and client type.",
    30722.15: "Parameters in GeoIp request are incomplete: city, state region and country must be supplied.",
    30722.16: "GeoIp lookup failed. Returned to the caller so they can take action, e.g., region selection.",
    30722.17: "The entity type is not recognized by the component.",
    30722.18: "no entity is found matching the type name and name provided.",
    30722.19: "the entity provided is recognized, but searching by name is not supported.",
    "30722.20": "A duplicate user already exists",
    30722.23: "The requested user with opt-in field disabled.",
    "30722.30": "Action not allowed for user opted-out of cross platform.",
    30722.31: "The action requested required a disallowed cross platform interaction.",
}
