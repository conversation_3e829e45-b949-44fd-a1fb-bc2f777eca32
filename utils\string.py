import random
import uuid


async def get_a_uuid() -> str:
    """返回一个uuid"""
    return str(uuid.uuid4())


def generate_random_str(random_length=16):
    """
    生成一个指定长度的随机字符串
    """
    random_str = ""
    base_str = "ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789"
    length = len(base_str) - 1
    for i in range(random_length):
        random_str += base_str[random.randint(0, length)]
    return random_str
