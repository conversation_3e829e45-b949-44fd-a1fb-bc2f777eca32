name: Update Repo Diagram

on:
  workflow_dispatch: { } # 保留手动触发方式
  schedule:
    - cron: "0 0 * * 0" # 每周日 00:00 执行一次

permissions:
  contents: write # 明确需要写入仓库内容

jobs:
  update-diagram:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout v3 branch
        uses: actions/checkout@v4
        with:
          ref: v3            # 明确 checkout v3分支
          fetch-depth: 0     # 获取完整历史提交记录

      - name: Check recent commits
        id: check_changes
        run: |
          # 获取过去7天的提交记录，排除repo-visualizer自动提交
          RECENT_COMMITS=$(git log --since="7 days ago" \
            --oneline --invert-grep \
            --grep="Repo visualizer: update diagram" | wc -l)

          # 记录最近提交数到step输出变量
          echo "recent_commits=$RECENT_COMMITS" >> $GITHUB_OUTPUT
          echo "Found $RECENT_COMMITS recent developer commits."

          # 若无其他提交则终止工作流
          if [ "$RECENT_COMMITS" -eq 0 ]; then
            echo "No developer commits in past 7 days. Skipping diagram update."
            exit 0
          fi

      - name: Update repository structure diagram
        if: steps.check_changes.outputs.recent_commits != '0'
        uses: githubocto/repo-visualizer@main
        with:
          excluded_paths: "ignore,.github"
