name: Pre-commit Checks

on:
  push:
    branches:
      - v3
  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - v3

permissions:
  contents: read
  pull-requests: write

jobs:
  pre-commit:
    name: Run Pre-commit
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [ "3.10", "3.11" ]  # 兼容 3.10 及以上

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'

      - name: Cache pre-commit
        uses: actions/cache@v3
        with:
          path: ~/.cache/pre-commit
          key: pre-commit-${{ runner.os }}-${{ hashFiles('.pre-commit-config.yaml') }}
          restore-keys: |
            pre-commit-${{ runner.os }}-

      - name: Install pre-commit
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit

      - name: Run pre-commit
        run: pre-commit run --all-files --show-diff-on-failure
