{"level": 1, "name": "HomoNumberConverter", "version": "0.1", "display_name": "恶臭数字转换", "authors": ["SAGIRI-kawaii", "移植by十三"], "description": "一个将复数域数字转换为114514格式的插件", "usage": ["在群中发送 `-homo {数字}` 即可，注意：这个数字应为实数或复数形式，若复数实部为0，则使用0+xi的格式"], "example": ["-homo 123456", "-homo 123.456", "-homo 123+456i", "-homo 123.456+789.012i", "-homo 123.456-789.012i"], "default_switch": true, "default_notice": false}