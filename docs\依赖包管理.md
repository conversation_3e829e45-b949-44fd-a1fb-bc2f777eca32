## Python依赖包管理

使用`uv`在项目根目录创建虚拟环境并管理依赖包。

### 安装 uv

参考[uv](https://docs.astral.sh/uv/getting-started/installation/#standalone-installer)官方文档

```shell
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

检查是否安装成功

```shell
uv help
```

### 常用操作

参考[uv](https://docs.astral.sh/uv/getting-started/features/#projects)官方文档

列出所有可用的python版本

```shell
uv python list
```

如果没有python，可以使用uv安装python

```shell
uv python install 3.9.7
```

使用指定版本的python创建虚拟环境，如果未指定版本，则使用系统默认版本，可使用`uv python find`查看系统默认的python版本

```shell
uv venv --python 3.11
```

从`pyproject.toml`安装依赖

```shell
uv sync
```

添加依赖

```shell
uv add <package-name>
```

删除依赖

```shell
uv remove <package-name>
```

创建`lock`文件

```shell
uv lock
```

查看依赖树

```shell
uv tree
```

使用uv run执行脚本

```shell
uv run <script-name>
```

使用uv运行python(如果此时未安装依赖，uv会自动安装依赖)

```shell
uv run main.py
```