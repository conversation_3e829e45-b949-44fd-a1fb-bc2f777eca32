﻿# bot基本配置
Master: Master # 填写bot主人的qq号
bot_accounts: # 填写你bot的qq号,支持以下格式：
  - 123456  # 直接使用数字格式
  - "987654"  # 字符串格式
  - account: 112233  # 字典格式，可配置独立连接参数
    mirai_host: "http://localhost:8081"  # 可选，覆盖全局mirai_host
    verify_key: "bot_specific_key"  # 可选，覆盖全局verify_key
default_account: default_account # 默认使用bot的qq账号
GroupMsg_log: true # 是否开启群消息日志
test_group: test_group # 填写测试群的群号,用于接收bot反馈信息等
proxy: proxy # 代理服务器的地址，目前暂时无效
auto_upgrade: true # 是否自动升级
debug_mode: false # 是否开启调试模式，调试模式下bot只响应Master的消息

# web管理相关
web_manager_api: false # 是否启用Web管理API
web_manager_auto_boot: false # 是否自动启动Web管理
api_port: 8080 # API端口
api_expose: false # API是否暴露给所有来源

# mah配置
mirai_host: http://localhost:8080 # Mirai HTTP 的监听地址
verify_key: verify_key # Mirai HTTP 通信密钥

# 数据库连接地址
db_link: sqlite+aiosqlite:///data.db

# 日志信息
log_related:
  common_retention: 7 # 一般日志的过期时间
  error_retention: 14 # 错误日志的过期时间

# 其他功能配置信息
functions:
  # 战地一
  bf1:
    default_account: default_account # bot的默认Player ID
    image_api: image_api # 使用何种图床,可为“smms”或"eac"
    image_apikey: image_apikey # 图床的API KEY
    apikey: apikey # BFEAC举报用API KEY

  # 识图
  image_search:
    saucenao_key: saucenao_key # SauceNAO的API KEY 可在saucenao.com获取

  # Steam游戏搜索 steamdb.info/app/app_id 的cookie
  steamdb_cookie: steamdb_cookie # SteamDB的cookie

  #龙图检测API
  dragon_detect:
    api_url: api_url
