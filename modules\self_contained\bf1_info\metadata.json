{"level": 1, "name": "bf1_info", "version": "0.2.1", "display_name": "BF1战绩", "authors": ["13"], "description": "查询战地一战绩的插件", "usage": ["-绑定 你的游戏名字,绑定后可使用以下指令查询信息", "-战绩 -武器 -载具 -信息", "-天眼查", "天眼查支持参数 a/b/v/o ", "如:-天眼查 -a 可查询拥有的管理", "-bf1rank 收藏/vip/admin/ban/owner", "支持参数 p/n", "p为页数,如: -bf1rank 收藏 -p2,", "n为名字,如: -bf1rank 收藏 -nBT,-bf1rank vip -n=SHlSAN13", "-交换", "交换支持参数 t", "t为搜索的时间,如 -交换 -t=2023.4.15", "-bfstat -bf1", "-搜服务器 服务器名字, -ss 服务器名字 ", "-详细服务器 服务器gameid, -ds 服务器gameid ", "查询武器/载具支持指令参数: -r -c -n -s -t", "-t为文字输出, -r为渲染行, -c为渲染列", "-n为搜索的名字, -s为排序类型支持kpm、命中(acc)、爆头率(hs)、时长(time)", "参数后的=为可选,如:-武器 -n10A -s=命中", "回复 -bf1百科+类型 可查看对应信息,支持类型:武器、载具、战略、战争、全世界"], "example": ["-绑定 shlsan13", "-武器 shlsan13 -> 默认图片输出", "-武器 shlsan13 -t -> 纯文字输出", "-步枪 shlsan13 -sKPM -> 查询步枪并按照KPM排序", "-机枪 -s爆头 -> 查询机枪并按照爆头排序", "-武器 -n=10A -> 查询武器名为10A的武器数据", "-武器 -n10A -sacc -> 查询武器名为10A的武器数据并按照命中率排序", "-武器 -行10 -列4 -> 查询武器并渲染为10行4列"], "default_switch": false, "default_notice": true}