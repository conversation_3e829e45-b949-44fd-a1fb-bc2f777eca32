{"level": 1, "name": "bf1_server_manager", "version": "0.2", "display_name": "BF1服管", "authors": ["13"], "description": "管理战地一服务器的插件", "usage": ["查询服务器：-服务器/-fwq/-FWQ/-服/-f/-狐务器/-负无穷", "查询服务器详情：上述查询指令后面加上 群组名和服务器序号", "如：-服务器 sakula1 即可查询群组sakula群组的第一个服务器的详情，如果当前QQ群绑定了群组，则可以省略群组名，如：-f1", "添加服主：-bfg 群组名 ao 服主QQ/@成员", "如：-bfg skula ao 123,@321 该指令只有服主有权限", "添加管理：-bfg 群组名 aa 管理员QQ/@成员", "如：-bfg skula aa @1,@2,123 该指令只有服主有权限,将服主改为管理也用这条", "删除权限：-bfg 群组名 del 管理员QQ/@成员", "如：-bfg skula del @1,@2,123 该指令只有服主有权限", "查询服管日志：-bflog+操作(可选)+群组名(可选)+ -qq=qq号 -pid=pid -name=displayName", "如：-bflog", "查询服内群友：-谁在玩/-谁在捞 群组名(可选)服务器序号", "如：-谁在玩sakula1 1, -谁在捞1", "查询玩家列表：-玩家列表/-playerlist/-pl/-lb+群组名(可选)服务器序号", "如：-pl sakula1, -lb1", "刷新session：-refresh 群组名(可选)服务器序号", "如：-refresh1, -refresh 1, -refresh sakula1", "踢出玩家：-kick/-踢/-k/-滚出+可选群组名+服务器序号+空格+玩家名+可选原因", "如：-kick sakula1 shlsan13 你好 (注意这里的sakula1的sakula为群组名,1为服务器序号，中间不加任何符号，服务器序号后一定要跟空格), -k1 shlsan13 你好", "踢出全部玩家：-清服/-炸服+可选群组名+服务器序号+空格+原因", "如：-清服 sakula1 晚安，-炸服1 全部睡觉!", "模糊搜索踢出: -sk/-searchkick+玩家名+可选原因", "如：-sk shlsan13 你好 ,-sk 条形码 不准条形码! (sk不能加服务器序号和群组名，只能在绑定了群组的群使用)", "封禁玩家：-ban/-封禁+可选群组名+服务器序号+空格+玩家名+可选原因", "如：-ban sakula1 shlsan13 你好, -ban1 shlsan13 你好", "解封玩家：-unban/-uban/-解封+可选群组名+服务器序号+空格+玩家名,解封不能加原因！", "如：-unban sakula1 shlsan13, -unban1 shlsan13", "全部封禁：-banall/-ba+空格+群组名+空格+玩家名+可选原因，全部封禁时不能加服务器序号只能(必须)写群组名", "如：-ba sakula 你好, -basakula shlsan13 你好", "全部解封：-unbanall/-uba+空格+玩家名+群组名，全部解封时不能加服务器序号只能(必须)写群组名", "如：-uba sakula shlsan13, -u<PERSON><PERSON><PERSON> shlsan13", "检查是否封禁玩家：-checkban+可选群组名+玩家名,不能写服务器序号", "如：-check<PERSON> sakula xia<PERSON>iao", "云封禁: -cb服务器序号 玩家名 原因,如: -cb1 shlsan13 test", "云解封: -ucb服务器序号 玩家名,如: -ub1 shlsan13", "查询云封禁列表: -cbl服务器序号,如: -cbl1,支持参数-page,-pid", "清理BAN位：-清理ban位/-清ban+可选群组名+服务器序号+可选数量，当不指定数量时默认为200(全部清理)", "如：-清理ban位 sakula1 100, -清ban1", "换边：-move/-换边/-挪+可选群组名+服务器序号+空格+玩家名+队伍ID/队伍名", "如：-move sakula1 shlsan13 1, -move1 shlsan13 意呆利", "换图：-map/-换图/-切图+可选群组名+服务器序号+空格+地图名/地图序号", "如：-map sakula1 要塞, -map1 重开", "图池换图：-图池/-maplist/-地图池+可选群组名+服务器序号", "如：-图池 sakula1, -maplist1", "加VIP：-vip/-v/-加v/-上v+可选群组名+服务器序号+空格+玩家名+可选时间(单位：天，可为负数)", "如：-vip sakula1 shlsan13 3, -vip1 shlsan13 -3", "下VIP：-unvip/-uvip/-删v/-下v/-减v+可选群组名+服务器序号+空格+玩家名,下v时不能写天数", "如：-unvip sakula1 shlsan13, -unvip1 shlsan13", "检查VIP：-checkvip+可选群组名+服务器序号", "如：-checkvip sakula1, -checkvip1 (行动服用于自动将缓存VIP生效/删除,并重开当前地图(非首图不重开但提示重开)，征服会清理VIP)", "VIP列表：-viplist/-vip列表/-vl+可选群组名+服务器序号", "如：-vlsakula1, -vl1, -vl sakula1", "BAN列表：-banlist/-ban列表/-bl/-封禁列表/-封禁list+可选群组名+服务器序号", "如：-bl sakula1, -bl1, -bl sakula1", "ADMIN列表：-adminlist/-管理列表/-al+可选群组名+服务器序号", "如：-al sakula1, -al1, -al sakula1", "BF1群组和服管号相关操作请使用：-help bf群组"], "example": ["-服务器 sakula1", "-谁在玩 sakula1 1", "-pl1 1", "-refresh1", "-kick1 shlsan13 你好", "-ban1 shlsan13 你好", "-unban1 shlsan13", "-banall sakula 你好", "-un<PERSON><PERSON> sakula s<PERSON>13", "-<PERSON><PERSON> sakula xia<PERSON>iao", "-清理ban位1 100", "-move1 shlsan13 1", "-map1 要塞", "-maplist1", "-vip1 shlsan13 3", "-unvip1 shlsan13", "-checkvip1", "-viplist", "-banlist1", "-adminlist1", "-help bf群组"], "default_switch": false, "default_notice": true}